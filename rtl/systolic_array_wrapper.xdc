# ============================================================================
# Xilinx Design Constraints (XDC) for Systolic Array Wrapper
# Target FPGA: Xilinx Zynq-7000 (ZedBoard/Pynq-Z1/Z2) or Kintex-7/Virtex-7
# Author: Generated for systolic_array_wrapper top-level module
# Usage: Top-level wrapper for Vivado synthesis and implementation
# ============================================================================

# ============================================================================
# Clock Constraints
# ============================================================================

# Primary clock input - 100MHz system clock
create_clock -period 10.000 -name sys_clk -waveform {0.000 5.000} [get_ports i_clk]

# Clock uncertainty and jitter budget
set_clock_uncertainty 0.050 [get_clocks sys_clk]
set_input_jitter [get_clocks sys_clk] 0.1

# Derive all clocks automatically
derive_pll_clocks
derive_clock_uncertainty

# ============================================================================
# Pin Assignment Constraints (ZedBoard/Pynq example)
# ============================================================================
# Modify these pin assignments based on your target FPGA board

# System clock - 100MHz differential clock (ZedBoard example)
set_property PACKAGE_PIN L16 [get_ports i_clk]
set_property IOSTANDARD LVCMOS25 [get_ports i_clk]

# Async reset - Push button or switch
set_property PACKAGE_PIN T16 [get_ports i_arst]
set_property IOSTANDARD LVCMOS25 [get_ports i_arst]

# Status LEDs
set_property PACKAGE_PIN M14 [get_ports o_led_status]
set_property IOSTANDARD LVCMOS25 [get_ports o_led_status]

set_property PACKAGE_PIN M15 [get_ports o_led_valid]
set_property IOSTANDARD LVCMOS25 [get_ports o_led_valid]


# ============================================================================
# Input/Output Timing Constraints
# ============================================================================

# Reset signal timing - asynchronous, no setup/hold requirements
set_false_path -from [get_ports i_arst]

# Output timing for LEDs - relaxed timing
set_output_delay -clock [get_clocks sys_clk] -min -1.000 [get_ports {o_led_status o_led_valid}]
set_output_delay -clock [get_clocks sys_clk] -max 2.000 [get_ports {o_led_status o_led_valid}]

# ============================================================================
# Internal Path Constraints
# ============================================================================

# Counter logic - allow for ripple carry delays
set_max_delay -from [get_cells */counter_reg*] -to [get_cells */valid_input*] 8.000

# Processing state machine timing
set_max_delay -from [get_cells */processing_reg] -to [get_ports o_led_status] 5.000

# Systolic array internal timing (inherited from submodule)
# These constraints ensure proper timing for the embedded systolic array

# Matrix data path timing - allow extra cycle for large multiplications
set_multicycle_path -setup -from [get_cells */matrix_a*] -through [get_cells */u_topSystolicArray/*] 2
set_multicycle_path -hold -from [get_cells */matrix_a*] -through [get_cells */u_topSystolicArray/*] 1

set_multicycle_path -setup -from [get_cells */matrix_b*] -through [get_cells */u_topSystolicArray/*] 2
set_multicycle_path -hold -from [get_cells */matrix_b*] -through [get_cells */u_topSystolicArray/*] 1

# ============================================================================
# Physical Constraints and Floorplanning
# ============================================================================

# Create placement region for systolic array to improve timing closure
create_pblock pblock_systolic_core
add_cells_to_pblock [get_pblocks pblock_systolic_core] [get_cells u_topSystolicArray]
resize_pblock [get_pblocks pblock_systolic_core] -add {SLICE_X20Y20:SLICE_X80Y80}
resize_pblock [get_pblocks pblock_systolic_core] -add {DSP48_X1Y8:DSP48_X3Y31}

# Constrain wrapper control logic to different region
create_pblock pblock_wrapper_ctrl
add_cells_to_pblock [get_pblocks pblock_wrapper_ctrl] [get_cells {counter_reg* valid_input_reg processing_reg checksum_reg*}]
resize_pblock [get_pblocks pblock_wrapper_ctrl] -add {SLICE_X0Y0:SLICE_X19Y19}

# ============================================================================
# DSP and Memory Resource Constraints
# ============================================================================

# DSP48 usage optimization for systolic array
# Each PE uses one DSP48E1 for 8x8 bit multiplication
set_property USE_DSP true [get_cells u_topSystolicArray/u_systolicArray/PerRow[*].PerCol[*].u_pe]

# Use block RAM for large shift registers if available
set_property RAM_STYLE block [get_cells u_topSystolicArray/perRowCol[*].row_q_reg*]
set_property RAM_STYLE block [get_cells u_topSystolicArray/perRowCol[*].col_q_reg*]

# ============================================================================
# Power Optimization
# ============================================================================

# Enable clock gating for power optimization
set_property CLOCK_GATING true [get_cells u_topSystolicArray]
set_property CLOCK_GATING true [get_cells counter_reg*]

# Enable power optimization
set_property POWER_OPT.PAR_NUM_FANOUT_LUT 12 [current_design]

# ============================================================================
# Timing Exceptions
# ============================================================================

# Cross-domain paths that can be relaxed
set_max_delay -from [get_cells checksum_reg*] -to [get_ports o_led_valid] 15.000

# False paths for debugging signals
set_false_path -to [get_ports o_led_*]

# ============================================================================
# Implementation Strategy
# ============================================================================

# Timing-driven implementation for best performance
set_property STRATEGY Performance_ExplorePostRoutePhysOpt [get_runs impl_1]
set_property STEPS.PHYS_OPT_DESIGN.IS_ENABLED true [get_runs impl_1]
set_property STEPS.POST_ROUTE_PHYS_OPT_DESIGN.IS_ENABLED true [get_runs impl_1]

# ============================================================================
# Synthesis Strategy
# ============================================================================

# Use Vivado synthesis with performance focus
set_property STRATEGY Vivado_Synthesis_Defaults [get_runs synth_1]
set_property STEPS.SYNTH_DESIGN.ARGS.FLATTEN_HIERARCHY none [get_runs synth_1]
set_property STEPS.SYNTH_DESIGN.ARGS.KEEP_EQUIVALENT_REGISTERS true [get_runs synth_1]

# ============================================================================
# Debug and Verification Constraints
# ============================================================================

# Mark debug signals for ILA insertion if needed
set_property MARK_DEBUG false [get_nets valid_input]
set_property MARK_DEBUG false [get_nets processing]
set_property MARK_DEBUG false [get_nets systolic_valid_result]
set_property MARK_DEBUG false [get_nets checksum*]

# Uncomment below for debugging
# set_property MARK_DEBUG true [get_nets valid_input]
# set_property MARK_DEBUG true [get_nets processing]
# set_property MARK_DEBUG true [get_nets systolic_valid_result]

# ============================================================================
# Bitstream Generation
# ============================================================================

# Bitstream compression and configuration
set_property BITSTREAM.GENERAL.COMPRESS TRUE [current_design]
set_property BITSTREAM.CONFIG.CONFIGRATE 33 [current_design]
set_property BITSTREAM.CONFIG.SPI_BUSWIDTH 4 [current_design]

# Configuration mode
set_property CONFIG_MODE SPIx4 [current_design]

# ============================================================================
# Timing Report Configuration
# ============================================================================

# Report timing summary after implementation
set_property STEPS.ROUTE_DESIGN.TCL.POST {
  report_timing_summary -delay_type min_max -report_unconstrained -check_timing_verbose -max_paths 10 -input_pins -file timing_summary.rpt
  report_utilization -file utilization.rpt
} [get_runs impl_1]

# ============================================================================
# Temperature and Voltage Constraints
# ============================================================================

# Operating conditions
set_operating_conditions -airflow 250 -heatsink medium -board small

# ============================================================================
# Board-Specific Pin Assignments
# ============================================================================
# Modify the following section based on your target board:

# For Pynq-Z1/Z2 boards:
# set_property PACKAGE_PIN H16 [get_ports i_clk]
# set_property PACKAGE_PIN D19 [get_ports i_arst]  
# set_property PACKAGE_PIN R14 [get_ports o_led_status]
# set_property PACKAGE_PIN P14 [get_ports o_led_valid]

# For Kintex-7 boards:
# set_property PACKAGE_PIN AD12 [get_ports i_clk]
# set_property PACKAGE_PIN AC6 [get_ports i_arst]
# set_property PACKAGE_PIN AB8 [get_ports o_led_status] 
# set_property PACKAGE_PIN AA8 [get_ports o_led_valid]

# ============================================================================
# Performance Targets and Expectations
# ============================================================================

# Target Performance:
# - Clock Frequency: 100 MHz (10ns period)
# - Resource Usage: ~N*N DSP48E1 blocks, ~(N*N*100) LUTs, ~(N*N*50) FFs
# - Matrix Multiplication Latency: (3*N-2) clock cycles
# - Throughput: One matrix multiplication every ~1024 cycles (test pattern)

# Expected Timing Closure:
# - Setup Slack: > 0.5ns
# - Hold Slack: > 0.1ns  
# - Pulse Width: > 4.5ns

# Resource Utilization Targets (N=4):
# - DSP48E1: 16 (100% utilization for 4x4 array)
# - LUTs: ~1600 (depends on FPGA family)
# - FFs: ~800 
# - BRAM: 0-2 (depending on synthesis optimization)

# ============================================================================
# Usage Notes
# ============================================================================

# 1. Modify pin assignments based on your target FPGA board
# 2. Adjust timing constraints if running at different clock frequencies
# 3. Scale resource constraints based on matrix size parameter N
# 4. Enable debug signals (MARK_DEBUG) for verification if needed
# 5. Consider adding more LEDs or external interfaces for result monitoring