`default_nettype none

module systolic_array_wrapper
  #(parameter int unsigned N = 4)
  ( input   logic  i_clk
  , input   logic  i_arst
  , output  logic  o_led_status    // LED to indicate processing status
  , output  logic  o_led_valid     // LED to indicate valid result
  );

  // Internal test matrices - fixed test data for demonstration
  // Matrix A: Simple pattern for testing
  logic [N-1:0][N-1:0][7:0] matrix_a;
  logic [N-1:0][N-1:0][7:0] matrix_b;
  
  // Generate simple test patterns
  for (genvar i = 0; i < N; i++) begin: gen_test_data_a
    for (genvar j = 0; j < N; j++) begin: gen_test_data_b
      always_comb begin
        // Simple pattern: matrix_a[i][j] = i + j + 1
        matrix_a[i][j] = 8'(i + j + 1);
        // Simple pattern: matrix_b[i][j] = (i * N) + j + 1  
        matrix_b[i][j] = 8'((i * N) + j + 1);
      end
    end: gen_test_data_b
  end: gen_test_data_a

  // Control logic
  logic [15:0] counter;
  logic valid_input;
  logic processing;
  
  // Counter for test sequence control
  always_ff @(posedge i_clk, posedge i_arst) begin
    if (i_arst) begin
      counter <= 16'h0;
    end else begin
      counter <= counter + 1'b1;
    end
  end
  
  // Generate periodic valid input signal every 1024 cycles
  always_comb begin
    valid_input = (counter[9:0] == 10'h000);
  end
  
  // Track processing state
  always_ff @(posedge i_clk, posedge i_arst) begin
    if (i_arst) begin
      processing <= 1'b0;
    end else if (valid_input) begin
      processing <= 1'b1;
    end else if (systolic_valid_result) begin
      processing <= 1'b0;
    end
  end

  // Output matrix from systolic array
  logic [N-1:0][N-1:0][31:0] result_matrix;
  logic systolic_valid_result;

  // Instantiate the systolic array
  topSystolicArray #(
    .N(N)
  ) u_topSystolicArray (
    .i_clk           (i_clk),
    .i_arst          (i_arst),
    .i_a             (matrix_a),
    .i_b             (matrix_b),
    .i_validInput    (valid_input),
    .o_c             (result_matrix),
    .o_validResult   (systolic_valid_result)
  );

  // LED outputs for status indication
  always_comb begin
    o_led_status = processing;           // LED on during processing
    o_led_valid  = systolic_valid_result; // LED pulse when result is valid
  end

  // Optional: Add result validation logic for debugging
  logic [31:0] checksum;
  logic checksum_valid;
  
  // Calculate simple checksum of result matrix for validation
  always_ff @(posedge i_clk, posedge i_arst) begin
    if (i_arst) begin
      checksum <= 32'h0;
      checksum_valid <= 1'b0;
    end else if (systolic_valid_result) begin
      checksum_valid <= 1'b1;
      // Simple XOR checksum of all result elements
      checksum <= ^{result_matrix[0][0], result_matrix[0][1], result_matrix[0][2], result_matrix[0][3],
                    result_matrix[1][0], result_matrix[1][1], result_matrix[1][2], result_matrix[1][3],
                    result_matrix[2][0], result_matrix[2][1], result_matrix[2][2], result_matrix[2][3],
                    result_matrix[3][0], result_matrix[3][1], result_matrix[3][2], result_matrix[3][3]};
    end else begin
      checksum_valid <= 1'b0;
    end
  end

endmodule

`resetall