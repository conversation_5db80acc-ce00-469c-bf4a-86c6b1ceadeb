# ============================================================================
# Xilinx Design Constraints (XDC) for 2D Systolic Array Multiplier
# Target FPGA: Xilinx Zynq-7000 (ZedBoard/Pynq-Z1/Z2) or Kintex-7/Virtex-7
# Author: Generated for topSystolicArray module (As Submodule)
# Usage: This module is used as a submodule in larger designs
# ============================================================================

# ============================================================================
# Clock Constraints
# ============================================================================

# Primary clock input - 100MHz system clock
# Note: Clock constraint still needed even as submodule for timing analysis
create_clock -period 10.000 -name sys_clk -waveform {0.000 5.000} [get_ports i_clk]

# Clock characteristics and jitter
set_input_jitter [get_clocks sys_clk] 0.1

# Clock group - all clocks derived from sys_clk are synchronous
set_clock_groups -asynchronous -group [get_clocks sys_clk]

# ============================================================================
# Input/Output Timing Constraints
# ============================================================================

# Input setup and hold times relative to clock
# Conservative timing for external inputs (control signals only)
set_input_delay -clock [get_clocks sys_clk] -min 1.000 [get_ports {i_arst i_validInput}]
set_input_delay -clock [get_clocks sys_clk] -max 3.000 [get_ports {i_arst i_validInput}]

# Matrix A input data constraints - TIMING ONLY (no pin assignment)
# These represent interface timing with parent module
set_input_delay -clock [get_clocks sys_clk] -min 0.500 [get_ports i_a*]
set_input_delay -clock [get_clocks sys_clk] -max 2.000 [get_ports i_a*]

# Matrix B input data constraints - TIMING ONLY (no pin assignment)
set_input_delay -clock [get_clocks sys_clk] -min 0.500 [get_ports i_b*]
set_input_delay -clock [get_clocks sys_clk] -max 2.000 [get_ports i_b*]

# Output timing constraints - TIMING ONLY (no pin assignment)
# Allow time for parent module to capture outputs
set_output_delay -clock [get_clocks sys_clk] -min -0.500 [get_ports o_validResult]
set_output_delay -clock [get_clocks sys_clk] -max 2.000 [get_ports o_validResult]

# Matrix C output data constraints - TIMING ONLY (no pin assignment)
set_output_delay -clock [get_clocks sys_clk] -min -0.500 [get_ports o_c*]
set_output_delay -clock [get_clocks sys_clk] -max 2.000 [get_ports o_c*]

# ============================================================================
# Pin Assignment Constraints (ONLY for actual external I/O)
# ============================================================================
# NOTE: Only assign pins for signals that actually connect to FPGA I/O
# Data ports (i_a, i_b, o_c) are internal and should NOT have pin assignments

# System clock - if this module is top-level and needs external clock
# COMMENT OUT if clock comes from parent module
# set_property PACKAGE_PIN Y9 [get_ports i_clk]
# set_property IOSTANDARD LVCMOS33 [get_ports i_clk]

# Reset signal - if this comes from external pin
# COMMENT OUT if reset comes from parent module  
# set_property PACKAGE_PIN P16 [get_ports i_arst]
# set_property IOSTANDARD LVCMOS33 [get_ports i_arst]

# Valid input signal - if this comes from external pin
# COMMENT OUT if control comes from parent module
# set_property PACKAGE_PIN R16 [get_ports i_validInput]
# set_property IOSTANDARD LVCMOS33 [get_ports i_validInput]

# Valid result output - if this goes to external pin
# COMMENT OUT if output goes to parent module
# set_property PACKAGE_PIN M14 [get_ports o_validResult]
# set_property IOSTANDARD LVCMOS33 [get_ports o_validResult]

# ============================================================================
# DATA PORTS - NO PIN ASSIGNMENTS NEEDED
# ============================================================================
# Matrix input/output ports (i_a, i_b, o_c) are internal connections
# DO NOT assign PACKAGE_PIN constraints for these ports
# Only I/O standards might be needed for synthesis optimization

# Optional I/O standards for synthesis optimization (usually not needed for internal signals)
# set_property IOSTANDARD LVCMOS33 [get_ports i_a*]
# set_property IOSTANDARD LVCMOS33 [get_ports i_b*] 
# set_property IOSTANDARD LVCMOS33 [get_ports o_c*]

# ============================================================================
# Physical Constraints and Optimization
# ============================================================================

# Place systolic array in specific region for optimal routing
# This helps with timing closure for large arrays
create_pblock pblock_systolic_array
add_cells_to_pblock [get_pblocks pblock_systolic_array] [get_cells u_systolicArray]
resize_pblock [get_pblocks pblock_systolic_array] -add {SLICE_X50Y50:SLICE_X100Y100}
resize_pblock [get_pblocks pblock_systolic_array] -add {DSP48_X2Y20:DSP48_X5Y39}

# Constrain DSP48 usage for multipliers in PEs  
# Each PE uses one DSP48 block for 8-bit multiplication
set_property LOC DSP48_X2Y20 [get_cells u_systolicArray/genblk1[0].genblk1[0].u_pe]
set_property LOC DSP48_X2Y21 [get_cells u_systolicArray/genblk1[0].genblk1[1].u_pe]
set_property LOC DSP48_X2Y22 [get_cells u_systolicArray/genblk1[0].genblk1[2].u_pe]
set_property LOC DSP48_X2Y23 [get_cells u_systolicArray/genblk1[0].genblk1[3].u_pe]

# ============================================================================
# Timing Exceptions and Constraints
# ============================================================================

# Asynchronous reset - no timing requirements
set_false_path -from [get_ports i_arst]

# Multi-cycle paths for matrix setup phase
# During i_validInput assertion, allow extra time for matrix transformation
set_multicycle_path -setup -from [get_ports i_a*] -through [get_pins */invertedRowElements*] 2
set_multicycle_path -hold -from [get_ports i_a*] -through [get_pins */invertedRowElements*] 1

set_multicycle_path -setup -from [get_ports i_b*] -through [get_pins */invertedColElements*] 2
set_multicycle_path -hold -from [get_ports i_b*] -through [get_pins */invertedColElements*] 1

# ============================================================================
# Power and Resource Optimization
# ============================================================================

# Enable clock gating for power optimization
set_property CLOCK_GATING true [get_cells u_systolicArray]

# Use BRAM for large shift registers if needed
set_property RAM_STYLE block [get_cells */row_q*]
set_property RAM_STYLE block [get_cells */col_q*]

# ============================================================================
# Debug and Verification Constraints  
# ============================================================================

# Mark critical signals for debugging
set_property MARK_DEBUG true [get_nets i_validInput]
set_property MARK_DEBUG true [get_nets o_validResult]
set_property MARK_DEBUG true [get_nets */counter_q*]
set_property MARK_DEBUG true [get_nets */doProcess_q]

# ILA (Integrated Logic Analyzer) depth
set_property C_DATA_DEPTH 1024 [get_debug_cores dbg_hub]

# ============================================================================
# Configuration and Bitstream Generation (for top-level only)
# ============================================================================

# Bitstream generation settings - only apply if this is top-level module
# set_property BITSTREAM.GENERAL.COMPRESS TRUE [current_design]
# set_property BITSTREAM.CONFIG.CONFIGRATE 33 [current_design]
# set_property BITSTREAM.CONFIG.SPI_BUSWIDTH 4 [current_design]

# Configuration mode (typically QSPI for modern FPGAs)
# set_property CONFIG_MODE SPIx4 [current_design]

# ============================================================================
# Implementation Strategy
# ============================================================================

# Use timing-driven placement and routing for better performance
set_property STRATEGY Performance_Explore [get_runs impl_1]
set_property STRATEGY Performance_ExplorePostRoutePhysOpt [get_runs impl_1]

# ============================================================================
# Notes and Documentation for Submodule Usage
# ============================================================================

# This XDC file is designed for Systolic Array used as SUBMODULE
# Key differences from top-level constraints:
# 1. Data ports (i_a, i_b, o_c) have NO pin assignments
# 2. Only timing constraints are applied to data interfaces
# 3. Control signals may or may not need pin assignments depending on usage
# 4. Bitstream generation settings are commented out

# For different matrix sizes (N), modify the following:
# 1. DSP48 location constraints based on array size (N*N DSP blocks needed)
# 2. Pblock size for systolic array placement
# 3. Timing constraints for larger data widths

# Performance targets for submodule:
# - Target Fmax: 100 MHz (10ns period)  
# - Expected resource usage: N*N DSP48, ~(N*N*60) LUTs, ~(N*N*30) FFs
# - Interface timing: 2ns setup, 0.5ns hold for internal connections

# Integration guidelines:
# 1. Parent module should provide stable clock and reset
# 2. Data interfaces should be registered in parent for best timing
# 3. Consider pipeline stages for high-frequency operation
# 4. Monitor resource utilization in context of complete design

# Synthesis strategy for submodules:
# - Use "out-of-context" synthesis for independent optimization
# - Apply timing constraints to enable proper optimization
# - Physical constraints help with deterministic placement 