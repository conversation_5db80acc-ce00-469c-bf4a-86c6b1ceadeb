{
    values = {
        "/usr/bin/gcc",
        {
            "-m64",
            "-g",
            "-O0",
            "-std=c++20",
            "-Ibuild/.gens/VtopSystolicArray/linux/x86_64/debug/platform/windows/idl",
            "-Ibuild/.gens/VtopSystolicArray/linux/x86_64/debug/rules/verilator",
            "-I/usr/local/share/verilator/include",
            "-I/usr/local/share/verilator/include/vltstd",
            "-DVM_COVERAGE=0",
            "-DVM_SC=0",
            "-DVM_THREADS=1",
            "-DVM_TIMING=0",
            "-DVM_TRACE_FST=0",
            "-DVM_TRACE_VCD=1"
        }
    },
    depfiles_format = "gcc",
    depfiles = "verilated.o: /usr/local/share/verilator/include/verilated.cpp  /usr/local/share/verilator/include/verilated_config.h  /usr/local/share/verilator/include/verilatedos.h  /usr/local/share/verilator/include/verilated_imp.h  /usr/local/share/verilator/include/verilated.h  /usr/local/share/verilator/include/verilated_types.h  /usr/local/share/verilator/include/verilated_funcs.h  /usr/local/share/verilator/include/verilated_syms.h  /usr/local/share/verilator/include/verilated_sym_props.h  /usr/local/share/verilator/include/verilated_threads.h  /usr/local/share/verilator/include/verilated_trace.h  /usr/local/share/verilator/include/verilatedos_c.h\
",
    files = {
        "/usr/local/share/verilator/include/verilated.cpp"
    }
}