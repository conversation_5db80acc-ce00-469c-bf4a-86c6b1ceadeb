{
    values = {
        "/usr/bin/ar",
        {
            "-cr"
        }
    },
    files = {
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/usr/local/share/verilator/include/verilated.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/usr/local/share/verilator/include/verilated_vcd_c.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/usr/local/share/verilator/include/verilated_threads.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/gens/rules/verilator/VtopSystolicArray___024root__Slow.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/gens/rules/verilator/VtopSystolicArray___024root__DepSet_h2c62118c__0__Slow.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/gens/rules/verilator/VtopSystolicArray___024root__DepSet_hff57d43e__0__Slow.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/gens/rules/verilator/VtopSystolicArray.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/gens/rules/verilator/VtopSystolicArray___024root__DepSet_h2c62118c__0.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/gens/rules/verilator/VtopSystolicArray___024root__DepSet_hff57d43e__0.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/gens/rules/verilator/VtopSystolicArray__Syms.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/gens/rules/verilator/VtopSystolicArray__Trace__0__Slow.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/gens/rules/verilator/VtopSystolicArray__TraceDecls__0__Slow.cpp.o",
        "build/.objs/VtopSystolicArray/linux/x86_64/debug/gens/rules/verilator/VtopSystolicArray__Trace__0.cpp.o"
    }
}