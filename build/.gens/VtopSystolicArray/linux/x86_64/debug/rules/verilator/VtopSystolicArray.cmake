# Verilated -*- CMake -*-
# DESCRIPTION: Verilator output: CMake include script with class lists
#
# This CMake script lists generated Verilated files, for including in higher level CMake scripts.
# This file is meant to be consumed by the verilate() function,
# which becomes available after executing `find_package(verilator).

### Constants...
set(PERL "perl" CACHE FILEPATH "Perl executable (from $PERL, defaults to 'perl' if not set)")
set(PYTHON3 "python3" CACHE FILEPATH "Python3 executable (from $PYTHON3, defaults to 'python3' if not set)")
set(VERILATOR_ROOT "/usr/local/share/verilator" CACHE PATH "Path to Verilator kit (from $VERILATOR_ROOT)")
set(VERILATOR_SOLVER "" CACHE STRING "Default SMT solver for constrained randomization (from $VERILATOR_SOLVER)")

### Compiler flags...
# User CFLAGS (from -CFLAGS on Verilator command line)
set(VtopSystolicArray_USER_CFLAGS )
# User LDLIBS (from -LDFLAGS on Verilator command line)
set(VtopSystolicArray_USER_LDLIBS )

### Switches...
# SystemC output mode?  0/1 (from --sc)
set(VtopSystolicArray_SC 0)
# Coverage output mode?  0/1 (from --coverage)
set(VtopSystolicArray_COVERAGE 0)
# Timing mode?  0/1
set(VtopSystolicArray_TIMING 0)
# Threaded output mode?  1/N threads (from --threads)
set(VtopSystolicArray_THREADS 1)
# FST Tracing output mode? 0/1 (from --trace-fst)
set(VtopSystolicArray_TRACE_FST 0)
# SAIF Tracing output mode? 0/1 (from --trace-saif)
set(VtopSystolicArray_TRACE_SAIF 0)
# VCD Tracing output mode?  0/1 (from --trace-vcd)
set(VtopSystolicArray_TRACE_VCD 1)

### Sources...
# Global classes, need linked once per executable
set(VtopSystolicArray_GLOBAL "${VERILATOR_ROOT}/include/verilated.cpp" "${VERILATOR_ROOT}/include/verilated_vcd_c.cpp" "${VERILATOR_ROOT}/include/verilated_threads.cpp" )
# Generated module classes, non-fast-path, compile with low/medium optimization
set(VtopSystolicArray_CLASSES_SLOW "build/.gens/VtopSystolicArray/linux/x86_64/debug/rules/verilator/VtopSystolicArray___024root__Slow.cpp" "build/.gens/VtopSystolicArray/linux/x86_64/debug/rules/verilator/VtopSystolicArray___024root__DepSet_h2c62118c__0__Slow.cpp" "build/.gens/VtopSystolicArray/linux/x86_64/debug/rules/verilator/VtopSystolicArray___024root__DepSet_hff57d43e__0__Slow.cpp" )
# Generated module classes, fast-path, compile with highest optimization
set(VtopSystolicArray_CLASSES_FAST "build/.gens/VtopSystolicArray/linux/x86_64/debug/rules/verilator/VtopSystolicArray.cpp" "build/.gens/VtopSystolicArray/linux/x86_64/debug/rules/verilator/VtopSystolicArray___024root__DepSet_h2c62118c__0.cpp" "build/.gens/VtopSystolicArray/linux/x86_64/debug/rules/verilator/VtopSystolicArray___024root__DepSet_hff57d43e__0.cpp" )
# Generated support classes, non-fast-path, compile with low/medium optimization
set(VtopSystolicArray_SUPPORT_SLOW "build/.gens/VtopSystolicArray/linux/x86_64/debug/rules/verilator/VtopSystolicArray__Syms.cpp" "build/.gens/VtopSystolicArray/linux/x86_64/debug/rules/verilator/VtopSystolicArray__Trace__0__Slow.cpp" "build/.gens/VtopSystolicArray/linux/x86_64/debug/rules/verilator/VtopSystolicArray__TraceDecls__0__Slow.cpp" )
# Generated support classes, fast-path, compile with highest optimization
set(VtopSystolicArray_SUPPORT_FAST "build/.gens/VtopSystolicArray/linux/x86_64/debug/rules/verilator/VtopSystolicArray__Trace__0.cpp" )
# All dependencies
set(VtopSystolicArray_DEPS "/usr/local/bin/verilator_bin" "/usr/local/share/verilator/include/verilated_std.sv" "/usr/local/share/verilator/include/verilated_std_waiver.vlt" "rtl/pe.sv" "rtl/systolicArray.sv" "rtl/topSystolicArray.sv" )
# User .cpp files (from .cpp's on Verilator command line)
set(VtopSystolicArray_USER_CLASSES )
