// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See VtopSystolicArray.h for the primary calling header

#include "VtopSystolicArray__pch.h"
#include "VtopSystolicArray___024root.h"

void VtopSystolicArray___024root___ico_sequent__TOP__0(VtopSystolicArray___024root* vlSelf);

void VtopSystolicArray___024root___eval_ico(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_ico\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1ULL & vlSelfRef.__VicoTriggered.word(0U))) {
        VtopSystolicArray___024root___ico_sequent__TOP__0(vlSelf);
        vlSelfRef.__Vm_traceActivity[1U] = 1U;
    }
}

VL_INLINE_OPT void VtopSystolicArray___024root___ico_sequent__TOP__0(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___ico_sequent__TOP__0\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelfRef.topSystolicArray__DOT__doProcess_d = 
        ((IData)(vlSelfRef.i_validInput) | ((0xbU != (IData)(vlSelfRef.topSystolicArray__DOT__counter_q)) 
                                            & (IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)));
    vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_33 
        = (((vlSelfRef.i_a[0U] << 0x18U) | (0xff0000U 
                                            & (vlSelfRef.i_a[0U] 
                                               << 8U))) 
           | ((0xff00U & (vlSelfRef.i_a[0U] >> 8U)) 
              | (vlSelfRef.i_a[0U] >> 0x18U)));
    vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_34 
        = (((vlSelfRef.i_b[0U] << 0x18U) | (0xff0000U 
                                            & (vlSelfRef.i_b[1U] 
                                               << 0x10U))) 
           | ((0xff00U & (vlSelfRef.i_b[2U] << 8U)) 
              | (0xffU & vlSelfRef.i_b[3U])));
    if (vlSelfRef.i_validInput) {
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & vlSelfRef.i_a[1U]))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_a[1U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_a[1U] 
                                                                              >> 8U)) 
                                                                          | (vlSelfRef.i_a[1U] 
                                                                             >> 0x18U)))))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & (vlSelfRef.i_b[0U] 
                                                                         >> 8U)))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_b[1U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & vlSelfRef.i_b[2U]) 
                                                                          | (0xffU 
                                                                             & (vlSelfRef.i_b[3U] 
                                                                                >> 8U))))))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & vlSelfRef.i_a[2U]))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_a[2U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_a[2U] 
                                                                              >> 8U)) 
                                                                          | (vlSelfRef.i_a[2U] 
                                                                             >> 0x18U)))))), 0x10U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & (vlSelfRef.i_b[0U] 
                                                                         >> 0x10U)))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & vlSelfRef.i_b[1U]) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_b[2U] 
                                                                              >> 8U)) 
                                                                          | (0xffU 
                                                                             & (vlSelfRef.i_b[3U] 
                                                                                >> 0x10U))))))), 0x10U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & vlSelfRef.i_a[3U]))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_a[3U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_a[3U] 
                                                                              >> 8U)) 
                                                                          | (vlSelfRef.i_a[3U] 
                                                                             >> 0x18U)))))), 0x18U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (vlSelfRef.i_b[0U] 
                                                                      >> 0x18U))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_b[1U] 
                                                                           >> 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_b[2U] 
                                                                              >> 0x10U)) 
                                                                          | (vlSelfRef.i_b[3U] 
                                                                             >> 0x18U)))))), 0x18U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (QData)((IData)(vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_33)));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (QData)((IData)(vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_34)));
    } else if ((0U != (IData)(vlSelfRef.topSystolicArray__DOT__counter_q))) {
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                                        << 0x28U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__row_q[2U])) 
                                                           << 8U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                                             >> 0x18U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                                        << 0x28U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__col_q[2U])) 
                                                           << 8U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                                             >> 0x18U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                                        << 0x30U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__row_q[4U])) 
                                                           << 0x10U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                                             >> 0x10U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                                        << 0x30U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__col_q[4U])) 
                                                           << 0x10U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                                             >> 0x10U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[6U])) 
                                                        << 0x18U) 
                                                       | ((QData)((IData)(
                                                                          vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                                          >> 8U))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[6U])) 
                                                        << 0x18U) 
                                                       | ((QData)((IData)(
                                                                          vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                                          >> 8U))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                                        << 0x20U) 
                                                       | (QData)((IData)(
                                                                         vlSelfRef.topSystolicArray__DOT__row_q[0U])))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                                        << 0x20U) 
                                                       | (QData)((IData)(
                                                                         vlSelfRef.topSystolicArray__DOT__col_q[0U])))), 8U));
    } else {
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                       << 0x28U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[2U])) 
                                        << 8U) | ((QData)((IData)(
                                                                  vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                                  >> 0x18U))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                       << 0x28U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[2U])) 
                                        << 8U) | ((QData)((IData)(
                                                                  vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                                  >> 0x18U))));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                       << 0x30U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[4U])) 
                                        << 0x10U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                        >> 0x10U))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                       << 0x30U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[4U])) 
                                        << 0x10U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                        >> 0x10U))));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[6U])) 
                                       << 0x38U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[6U])) 
                                        << 0x18U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                        >> 8U))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[6U])) 
                                       << 0x38U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[6U])) 
                                        << 0x18U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                        >> 8U))));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                       << 0x20U) | (QData)((IData)(
                                                                   vlSelfRef.topSystolicArray__DOT__row_q[0U]))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                       << 0x20U) | (QData)((IData)(
                                                                   vlSelfRef.topSystolicArray__DOT__col_q[0U]))));
    }
    vlSelfRef.topSystolicArray__DOT__counter_d = ((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_d)
                                                   ? 
                                                  (0xfU 
                                                   & ((IData)(1U) 
                                                      + (IData)(vlSelfRef.topSystolicArray__DOT__counter_q)))
                                                   : 0U);
}

void VtopSystolicArray___024root___eval_triggers__ico(VtopSystolicArray___024root* vlSelf);

bool VtopSystolicArray___024root___eval_phase__ico(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_phase__ico\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    CData/*0:0*/ __VicoExecute;
    // Body
    VtopSystolicArray___024root___eval_triggers__ico(vlSelf);
    __VicoExecute = vlSelfRef.__VicoTriggered.any();
    if (__VicoExecute) {
        VtopSystolicArray___024root___eval_ico(vlSelf);
    }
    return (__VicoExecute);
}

void VtopSystolicArray___024root___eval_act(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_act\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
}

void VtopSystolicArray___024root___nba_sequent__TOP__0(VtopSystolicArray___024root* vlSelf);

void VtopSystolicArray___024root___eval_nba(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_nba\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((3ULL & vlSelfRef.__VnbaTriggered.word(0U))) {
        VtopSystolicArray___024root___nba_sequent__TOP__0(vlSelf);
        vlSelfRef.__Vm_traceActivity[2U] = 1U;
    }
}

VL_INLINE_OPT void VtopSystolicArray___024root___nba_sequent__TOP__0(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___nba_sequent__TOP__0\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    CData/*7:0*/ __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = 0;
    CData/*7:0*/ __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q = 0;
    CData/*7:0*/ __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = 0;
    CData/*7:0*/ __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q = 0;
    CData/*7:0*/ __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q = 0;
    CData/*7:0*/ __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q = 0;
    CData/*7:0*/ __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = 0;
    CData/*7:0*/ __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = 0;
    // Body
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
        = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q 
        = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
        = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q 
        = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q 
        = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q 
        = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
        = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
        = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    vlSelfRef.topSystolicArray__DOT__validResult_q 
        = ((~ (IData)(vlSelfRef.i_arst)) & (0xaU == (IData)(vlSelfRef.topSystolicArray__DOT__counter_q)));
    if (vlSelfRef.i_arst) {
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q = 0U;
        __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q = 0U;
        __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q = 0U;
        __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = 0U;
        __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q = 0U;
        __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q = 0U;
        __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q = 0U;
        __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q = 0U;
        __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q = 0U;
        vlSelfRef.topSystolicArray__DOT__counter_q = 0U;
    } else {
        if (vlSelfRef.topSystolicArray__DOT__doProcess_q) {
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q 
                = (0xffU & vlSelfRef.topSystolicArray__DOT__row_q[0U]);
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q 
                = (0xffU & vlSelfRef.topSystolicArray__DOT__col_q[0U]);
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q 
                = (0xffU & ((vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                             << 8U) | (vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                                       >> 0x18U)));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q 
                = (0xffU & ((vlSelfRef.topSystolicArray__DOT__col_q[3U] 
                             << 0x10U) | (vlSelfRef.topSystolicArray__DOT__col_q[3U] 
                                          >> 0x10U)));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q 
                = (0xffU & ((vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                             << 0x18U) | (vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                                          >> 8U)));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q 
                = (0xffU & ((vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                             << 8U) | (vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                                       >> 0x18U)));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q 
                = (0xffU & ((vlSelfRef.topSystolicArray__DOT__row_q[3U] 
                             << 0x10U) | (vlSelfRef.topSystolicArray__DOT__row_q[3U] 
                                          >> 0x10U)));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q 
                = (0xffU & ((vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                             << 0x18U) | (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                                          >> 8U)));
        } else {
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
            __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q 
                = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q 
                = (0xffU & (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q 
                = (0xffU & (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q 
                = (0xffU & (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q 
                = (0xffU & (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q 
                = (0xffU & (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q 
                = (0xffU & (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q 
                = (0xffU & (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q));
            vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q 
                = (0xffU & (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q));
        }
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
            = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d;
        vlSelfRef.topSystolicArray__DOT__counter_q 
            = vlSelfRef.topSystolicArray__DOT__counter_d;
    }
    vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
        = __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
        = __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q 
        = __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
    vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q 
        = __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
    vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
        = __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q 
        = __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
    vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q 
        = __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
    vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q 
        = __Vdly__topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
    vlSelfRef.topSystolicArray__DOT__col_q[0U] = (IData)(
                                                         ((IData)(vlSelfRef.i_arst)
                                                           ? 0ULL
                                                           : vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__));
    vlSelfRef.topSystolicArray__DOT__col_q[1U] = (((IData)(
                                                           ((IData)(vlSelfRef.i_arst)
                                                             ? 0ULL
                                                             : vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__)) 
                                                   << 0x18U) 
                                                  | (IData)(
                                                            (((IData)(vlSelfRef.i_arst)
                                                               ? 0ULL
                                                               : vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__) 
                                                             >> 0x20U)));
    vlSelfRef.topSystolicArray__DOT__col_q[2U] = (((IData)(
                                                           ((IData)(vlSelfRef.i_arst)
                                                             ? 0ULL
                                                             : vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__)) 
                                                   >> 8U) 
                                                  | ((IData)(
                                                             (((IData)(vlSelfRef.i_arst)
                                                                ? 0ULL
                                                                : vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__) 
                                                              >> 0x20U)) 
                                                     << 0x18U));
    vlSelfRef.topSystolicArray__DOT__col_q[3U] = (((IData)(
                                                           ((IData)(vlSelfRef.i_arst)
                                                             ? 0ULL
                                                             : vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__)) 
                                                   << 0x10U) 
                                                  | ((IData)(
                                                             (((IData)(vlSelfRef.i_arst)
                                                                ? 0ULL
                                                                : vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__) 
                                                              >> 0x20U)) 
                                                     >> 8U));
    vlSelfRef.topSystolicArray__DOT__col_q[4U] = (((IData)(
                                                           ((IData)(vlSelfRef.i_arst)
                                                             ? 0ULL
                                                             : vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__)) 
                                                   >> 0x10U) 
                                                  | ((IData)(
                                                             (((IData)(vlSelfRef.i_arst)
                                                                ? 0ULL
                                                                : vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__) 
                                                              >> 0x20U)) 
                                                     << 0x10U));
    vlSelfRef.topSystolicArray__DOT__col_q[5U] = ((0xffffff00U 
                                                   & vlSelfRef.topSystolicArray__DOT__col_q[5U]) 
                                                  | ((IData)(
                                                             (((IData)(vlSelfRef.i_arst)
                                                                ? 0ULL
                                                                : vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__) 
                                                              >> 0x20U)) 
                                                     >> 0x10U));
    vlSelfRef.topSystolicArray__DOT__col_q[5U] = ((0xffU 
                                                   & vlSelfRef.topSystolicArray__DOT__col_q[5U]) 
                                                  | ((IData)(
                                                             ((IData)(vlSelfRef.i_arst)
                                                               ? 0ULL
                                                               : vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__)) 
                                                     << 8U));
    vlSelfRef.topSystolicArray__DOT__col_q[6U] = (((IData)(
                                                           ((IData)(vlSelfRef.i_arst)
                                                             ? 0ULL
                                                             : vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__)) 
                                                   >> 0x18U) 
                                                  | ((IData)(
                                                             (((IData)(vlSelfRef.i_arst)
                                                                ? 0ULL
                                                                : vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__) 
                                                              >> 0x20U)) 
                                                     << 8U));
    vlSelfRef.topSystolicArray__DOT__doProcess_q = 
        ((~ (IData)(vlSelfRef.i_arst)) & (IData)(vlSelfRef.topSystolicArray__DOT__doProcess_d));
    vlSelfRef.topSystolicArray__DOT__row_q[0U] = (IData)(
                                                         ((IData)(vlSelfRef.i_arst)
                                                           ? 0ULL
                                                           : vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__));
    vlSelfRef.topSystolicArray__DOT__row_q[1U] = (((IData)(
                                                           ((IData)(vlSelfRef.i_arst)
                                                             ? 0ULL
                                                             : vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__)) 
                                                   << 0x18U) 
                                                  | (IData)(
                                                            (((IData)(vlSelfRef.i_arst)
                                                               ? 0ULL
                                                               : vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__) 
                                                             >> 0x20U)));
    vlSelfRef.topSystolicArray__DOT__row_q[2U] = (((IData)(
                                                           ((IData)(vlSelfRef.i_arst)
                                                             ? 0ULL
                                                             : vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__)) 
                                                   >> 8U) 
                                                  | ((IData)(
                                                             (((IData)(vlSelfRef.i_arst)
                                                                ? 0ULL
                                                                : vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__) 
                                                              >> 0x20U)) 
                                                     << 0x18U));
    vlSelfRef.topSystolicArray__DOT__row_q[3U] = (((IData)(
                                                           ((IData)(vlSelfRef.i_arst)
                                                             ? 0ULL
                                                             : vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__)) 
                                                   << 0x10U) 
                                                  | ((IData)(
                                                             (((IData)(vlSelfRef.i_arst)
                                                                ? 0ULL
                                                                : vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__) 
                                                              >> 0x20U)) 
                                                     >> 8U));
    vlSelfRef.topSystolicArray__DOT__row_q[4U] = (((IData)(
                                                           ((IData)(vlSelfRef.i_arst)
                                                             ? 0ULL
                                                             : vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__)) 
                                                   >> 0x10U) 
                                                  | ((IData)(
                                                             (((IData)(vlSelfRef.i_arst)
                                                                ? 0ULL
                                                                : vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__) 
                                                              >> 0x20U)) 
                                                     << 0x10U));
    vlSelfRef.topSystolicArray__DOT__row_q[5U] = ((0xffffff00U 
                                                   & vlSelfRef.topSystolicArray__DOT__row_q[5U]) 
                                                  | ((IData)(
                                                             (((IData)(vlSelfRef.i_arst)
                                                                ? 0ULL
                                                                : vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__) 
                                                              >> 0x20U)) 
                                                     >> 0x10U));
    vlSelfRef.topSystolicArray__DOT__row_q[5U] = ((0xffU 
                                                   & vlSelfRef.topSystolicArray__DOT__row_q[5U]) 
                                                  | ((IData)(
                                                             ((IData)(vlSelfRef.i_arst)
                                                               ? 0ULL
                                                               : vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__)) 
                                                     << 8U));
    vlSelfRef.topSystolicArray__DOT__row_q[6U] = (((IData)(
                                                           ((IData)(vlSelfRef.i_arst)
                                                             ? 0ULL
                                                             : vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__)) 
                                                   >> 0x18U) 
                                                  | ((IData)(
                                                             (((IData)(vlSelfRef.i_arst)
                                                                ? 0ULL
                                                                : vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__) 
                                                              >> 0x20U)) 
                                                     << 8U));
    vlSelfRef.o_c[0U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[1U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[2U] = (IData)((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                  << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))));
    vlSelfRef.o_c[3U] = (IData)(((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                   << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))) 
                                 >> 0x20U));
    vlSelfRef.o_c[4U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[5U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[6U] = (IData)((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                  << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))));
    vlSelfRef.o_c[7U] = (IData)(((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                   << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))) 
                                 >> 0x20U));
    vlSelfRef.o_c[8U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[9U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[0xaU] = (IData)((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                    << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))));
    vlSelfRef.o_c[0xbU] = (IData)(((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                     << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))) 
                                   >> 0x20U));
    vlSelfRef.o_c[0xcU] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[0xdU] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[0xeU] = (IData)((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                    << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))));
    vlSelfRef.o_c[0xfU] = (IData)(((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                     << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))) 
                                   >> 0x20U));
    vlSelfRef.o_validResult = vlSelfRef.topSystolicArray__DOT__validResult_q;
    if (vlSelfRef.topSystolicArray__DOT__doProcess_q) {
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                  * (vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                     >> 0x18U)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                  * (0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[3U] 
                              >> 0x10U))));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                  * (0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                              >> 8U))));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
               + ((0xffU & vlSelfRef.topSystolicArray__DOT__row_q[0U]) 
                  * (0xffU & vlSelfRef.topSystolicArray__DOT__col_q[0U])));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
               + ((vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                   >> 0x18U) * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
               + ((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[3U] 
                            >> 0x10U)) * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
               + ((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                            >> 8U)) * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)));
    } else {
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = 0U;
    }
    if (vlSelfRef.i_validInput) {
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (QData)((IData)(vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_33)));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (QData)((IData)(vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_34)));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & vlSelfRef.i_a[1U]))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_a[1U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_a[1U] 
                                                                              >> 8U)) 
                                                                          | (vlSelfRef.i_a[1U] 
                                                                             >> 0x18U)))))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & (vlSelfRef.i_b[0U] 
                                                                         >> 8U)))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_b[1U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & vlSelfRef.i_b[2U]) 
                                                                          | (0xffU 
                                                                             & (vlSelfRef.i_b[3U] 
                                                                                >> 8U))))))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & vlSelfRef.i_a[2U]))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_a[2U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_a[2U] 
                                                                              >> 8U)) 
                                                                          | (vlSelfRef.i_a[2U] 
                                                                             >> 0x18U)))))), 0x10U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & (vlSelfRef.i_b[0U] 
                                                                         >> 0x10U)))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & vlSelfRef.i_b[1U]) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_b[2U] 
                                                                              >> 8U)) 
                                                                          | (0xffU 
                                                                             & (vlSelfRef.i_b[3U] 
                                                                                >> 0x10U))))))), 0x10U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & vlSelfRef.i_a[3U]))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_a[3U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_a[3U] 
                                                                              >> 8U)) 
                                                                          | (vlSelfRef.i_a[3U] 
                                                                             >> 0x18U)))))), 0x18U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (vlSelfRef.i_b[0U] 
                                                                      >> 0x18U))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_b[1U] 
                                                                           >> 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_b[2U] 
                                                                              >> 0x10U)) 
                                                                          | (vlSelfRef.i_b[3U] 
                                                                             >> 0x18U)))))), 0x18U));
    } else if ((0U != (IData)(vlSelfRef.topSystolicArray__DOT__counter_q))) {
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                                        << 0x20U) 
                                                       | (QData)((IData)(
                                                                         vlSelfRef.topSystolicArray__DOT__row_q[0U])))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                                        << 0x20U) 
                                                       | (QData)((IData)(
                                                                         vlSelfRef.topSystolicArray__DOT__col_q[0U])))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                                        << 0x28U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__row_q[2U])) 
                                                           << 8U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                                             >> 0x18U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                                        << 0x28U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__col_q[2U])) 
                                                           << 8U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                                             >> 0x18U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                                        << 0x30U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__row_q[4U])) 
                                                           << 0x10U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                                             >> 0x10U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                                        << 0x30U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__col_q[4U])) 
                                                           << 0x10U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                                             >> 0x10U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[6U])) 
                                                        << 0x18U) 
                                                       | ((QData)((IData)(
                                                                          vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                                          >> 8U))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[6U])) 
                                                        << 0x18U) 
                                                       | ((QData)((IData)(
                                                                          vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                                          >> 8U))), 8U));
    } else {
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                       << 0x20U) | (QData)((IData)(
                                                                   vlSelfRef.topSystolicArray__DOT__row_q[0U]))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                       << 0x20U) | (QData)((IData)(
                                                                   vlSelfRef.topSystolicArray__DOT__col_q[0U]))));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                       << 0x28U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[2U])) 
                                        << 8U) | ((QData)((IData)(
                                                                  vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                                  >> 0x18U))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                       << 0x28U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[2U])) 
                                        << 8U) | ((QData)((IData)(
                                                                  vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                                  >> 0x18U))));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                       << 0x30U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[4U])) 
                                        << 0x10U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                        >> 0x10U))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                       << 0x30U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[4U])) 
                                        << 0x10U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                        >> 0x10U))));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[6U])) 
                                       << 0x38U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[6U])) 
                                        << 0x18U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                        >> 8U))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[6U])) 
                                       << 0x38U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[6U])) 
                                        << 0x18U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                        >> 8U))));
    }
    vlSelfRef.topSystolicArray__DOT__doProcess_d = 
        ((IData)(vlSelfRef.i_validInput) | ((0xbU != (IData)(vlSelfRef.topSystolicArray__DOT__counter_q)) 
                                            & (IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)));
    vlSelfRef.topSystolicArray__DOT__counter_d = ((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_d)
                                                   ? 
                                                  (0xfU 
                                                   & ((IData)(1U) 
                                                      + (IData)(vlSelfRef.topSystolicArray__DOT__counter_q)))
                                                   : 0U);
}

void VtopSystolicArray___024root___eval_triggers__act(VtopSystolicArray___024root* vlSelf);

bool VtopSystolicArray___024root___eval_phase__act(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_phase__act\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    VlTriggerVec<2> __VpreTriggered;
    CData/*0:0*/ __VactExecute;
    // Body
    VtopSystolicArray___024root___eval_triggers__act(vlSelf);
    __VactExecute = vlSelfRef.__VactTriggered.any();
    if (__VactExecute) {
        __VpreTriggered.andNot(vlSelfRef.__VactTriggered, vlSelfRef.__VnbaTriggered);
        vlSelfRef.__VnbaTriggered.thisOr(vlSelfRef.__VactTriggered);
        VtopSystolicArray___024root___eval_act(vlSelf);
    }
    return (__VactExecute);
}

bool VtopSystolicArray___024root___eval_phase__nba(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_phase__nba\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    CData/*0:0*/ __VnbaExecute;
    // Body
    __VnbaExecute = vlSelfRef.__VnbaTriggered.any();
    if (__VnbaExecute) {
        VtopSystolicArray___024root___eval_nba(vlSelf);
        vlSelfRef.__VnbaTriggered.clear();
    }
    return (__VnbaExecute);
}

#ifdef VL_DEBUG
VL_ATTR_COLD void VtopSystolicArray___024root___dump_triggers__ico(VtopSystolicArray___024root* vlSelf);
#endif  // VL_DEBUG
#ifdef VL_DEBUG
VL_ATTR_COLD void VtopSystolicArray___024root___dump_triggers__nba(VtopSystolicArray___024root* vlSelf);
#endif  // VL_DEBUG
#ifdef VL_DEBUG
VL_ATTR_COLD void VtopSystolicArray___024root___dump_triggers__act(VtopSystolicArray___024root* vlSelf);
#endif  // VL_DEBUG

void VtopSystolicArray___024root___eval(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    IData/*31:0*/ __VicoIterCount;
    CData/*0:0*/ __VicoContinue;
    IData/*31:0*/ __VnbaIterCount;
    CData/*0:0*/ __VnbaContinue;
    // Body
    __VicoIterCount = 0U;
    vlSelfRef.__VicoFirstIteration = 1U;
    __VicoContinue = 1U;
    while (__VicoContinue) {
        if (VL_UNLIKELY(((0x64U < __VicoIterCount)))) {
#ifdef VL_DEBUG
            VtopSystolicArray___024root___dump_triggers__ico(vlSelf);
#endif
            VL_FATAL_MT("rtl/topSystolicArray.sv", 3, "", "Input combinational region did not converge.");
        }
        __VicoIterCount = ((IData)(1U) + __VicoIterCount);
        __VicoContinue = 0U;
        if (VtopSystolicArray___024root___eval_phase__ico(vlSelf)) {
            __VicoContinue = 1U;
        }
        vlSelfRef.__VicoFirstIteration = 0U;
    }
    __VnbaIterCount = 0U;
    __VnbaContinue = 1U;
    while (__VnbaContinue) {
        if (VL_UNLIKELY(((0x64U < __VnbaIterCount)))) {
#ifdef VL_DEBUG
            VtopSystolicArray___024root___dump_triggers__nba(vlSelf);
#endif
            VL_FATAL_MT("rtl/topSystolicArray.sv", 3, "", "NBA region did not converge.");
        }
        __VnbaIterCount = ((IData)(1U) + __VnbaIterCount);
        __VnbaContinue = 0U;
        vlSelfRef.__VactIterCount = 0U;
        vlSelfRef.__VactContinue = 1U;
        while (vlSelfRef.__VactContinue) {
            if (VL_UNLIKELY(((0x64U < vlSelfRef.__VactIterCount)))) {
#ifdef VL_DEBUG
                VtopSystolicArray___024root___dump_triggers__act(vlSelf);
#endif
                VL_FATAL_MT("rtl/topSystolicArray.sv", 3, "", "Active region did not converge.");
            }
            vlSelfRef.__VactIterCount = ((IData)(1U) 
                                         + vlSelfRef.__VactIterCount);
            vlSelfRef.__VactContinue = 0U;
            if (VtopSystolicArray___024root___eval_phase__act(vlSelf)) {
                vlSelfRef.__VactContinue = 1U;
            }
        }
        if (VtopSystolicArray___024root___eval_phase__nba(vlSelf)) {
            __VnbaContinue = 1U;
        }
    }
}

#ifdef VL_DEBUG
void VtopSystolicArray___024root___eval_debug_assertions(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_debug_assertions\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if (VL_UNLIKELY(((vlSelfRef.i_clk & 0xfeU)))) {
        Verilated::overWidthError("i_clk");}
    if (VL_UNLIKELY(((vlSelfRef.i_arst & 0xfeU)))) {
        Verilated::overWidthError("i_arst");}
    if (VL_UNLIKELY(((vlSelfRef.i_validInput & 0xfeU)))) {
        Verilated::overWidthError("i_validInput");}
}
#endif  // VL_DEBUG
