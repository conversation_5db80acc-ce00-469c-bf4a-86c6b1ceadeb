// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design internal header
// See VtopSystolicArray.h for the primary calling header

#ifndef VERILATED_VTOPSYSTOLICARRAY___024ROOT_H_
#define VERILATED_VTOPSYSTOLICARRAY___024ROOT_H_  // guard

#include "verilated.h"


class VtopSystolicArray__Syms;

class alignas(VL_CACHE_LINE_BYTES) VtopSystolicArray___024root final : public VerilatedModule {
  public:

    // DESIGN SPECIFIC STATE
    // Anonymous structures to workaround compiler member-count bugs
    struct {
        VL_IN8(i_clk,0,0);
        VL_IN8(i_arst,0,0);
        VL_INW(i_a,127,0,4);
        VL_INW(i_b,127,0,4);
        VL_IN8(i_validInput,0,0);
        VL_OUT8(o_validResult,0,0);
        CData/*3:0*/ topSystolicArray__DOT__counter_d;
        CData/*3:0*/ topSystolicArray__DOT__counter_q;
        CData/*0:0*/ topSystolicArray__DOT__validResult_q;
        CData/*0:0*/ topSystolicArray__DOT__doProcess_d;
        CData/*0:0*/ topSystolicArray__DOT__doProcess_q;
        VlWide<7>/*223:0*/ topSystolicArray__DOT__row_q;
        VlWide<7>/*223:0*/ topSystolicArray__DOT__col_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q;
        CData/*7:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q;
        CData/*0:0*/ __VstlFirstIteration;
        CData/*0:0*/ __VicoFirstIteration;
        CData/*0:0*/ __Vtrigprevexpr___TOP__i_arst__0;
        CData/*0:0*/ __Vtrigprevexpr___TOP__i_clk__0;
        CData/*0:0*/ __VactContinue;
        VL_OUTW(o_c,511,0,16);
        IData/*31:0*/ topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_33;
        IData/*31:0*/ topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_34;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d;
    };
    struct {
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d;
        IData/*31:0*/ topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q;
        IData/*31:0*/ __VactIterCount;
        QData/*55:0*/ topSystolicArray__DOT__row_d__BRA__223__03a168__KET__;
        QData/*55:0*/ topSystolicArray__DOT__row_d__BRA__167__03a112__KET__;
        QData/*55:0*/ topSystolicArray__DOT__row_d__BRA__111__03a56__KET__;
        QData/*55:0*/ topSystolicArray__DOT__row_d__BRA__55__03a0__KET__;
        QData/*55:0*/ topSystolicArray__DOT__col_d__BRA__223__03a168__KET__;
        QData/*55:0*/ topSystolicArray__DOT__col_d__BRA__167__03a112__KET__;
        QData/*55:0*/ topSystolicArray__DOT__col_d__BRA__111__03a56__KET__;
        QData/*55:0*/ topSystolicArray__DOT__col_d__BRA__55__03a0__KET__;
        VlUnpacked<CData/*0:0*/, 3> __Vm_traceActivity;
    };
    VlTriggerVec<1> __VstlTriggered;
    VlTriggerVec<1> __VicoTriggered;
    VlTriggerVec<2> __VactTriggered;
    VlTriggerVec<2> __VnbaTriggered;

    // INTERNAL VARIABLES
    VtopSystolicArray__Syms* const vlSymsp;

    // CONSTRUCTORS
    VtopSystolicArray___024root(VtopSystolicArray__Syms* symsp, const char* v__name);
    ~VtopSystolicArray___024root();
    VL_UNCOPYABLE(VtopSystolicArray___024root);

    // INTERNAL METHODS
    void __Vconfigure(bool first);
};


#endif  // guard
