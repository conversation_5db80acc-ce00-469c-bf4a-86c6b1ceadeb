// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Tracing implementation internals
#include "verilated_vcd_c.h"
#include "VtopSystolicArray__Syms.h"


void VtopSystolicArray___024root__trace_chg_0_sub_0(VtopSystolicArray___024root* vlSelf, VerilatedVcd::Buffer* bufp);

void VtopSystolicArray___024root__trace_chg_0(void* voidSelf, VerilatedVcd::Buffer* bufp) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root__trace_chg_0\n"); );
    // Init
    VtopSystolicArray___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<VtopSystolicArray___024root*>(voidSelf);
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    if (VL_UNLIKELY(!vlSymsp->__Vm_activity)) return;
    // Body
    VtopSystolicArray___024root__trace_chg_0_sub_0((&vlSymsp->TOP), bufp);
}

void VtopSystolicArray___024root__trace_chg_0_sub_0(VtopSystolicArray___024root* vlSelf, VerilatedVcd::Buffer* bufp) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root__trace_chg_0_sub_0\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    uint32_t* const oldp VL_ATTR_UNUSED = bufp->oldp(vlSymsp->__Vm_baseCode + 1);
    VlWide<7>/*223:0*/ __Vtemp_3;
    VlWide<7>/*223:0*/ __Vtemp_6;
    VlWide<5>/*159:0*/ __Vtemp_9;
    VlWide<5>/*159:0*/ __Vtemp_12;
    VlWide<4>/*127:0*/ __Vtemp_14;
    VlWide<4>/*127:0*/ __Vtemp_16;
    // Body
    if (VL_UNLIKELY(((vlSelfRef.__Vm_traceActivity[1U] 
                      | vlSelfRef.__Vm_traceActivity
                      [2U])))) {
        bufp->chgCData(oldp+0,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_d)
                                 ? (0xfU & ((IData)(1U) 
                                            + (IData)(vlSelfRef.topSystolicArray__DOT__counter_q)))
                                 : 0U)),4);
        bufp->chgBit(oldp+1,(vlSelfRef.topSystolicArray__DOT__doProcess_d));
        __Vtemp_3[0U] = (IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__);
        __Vtemp_3[1U] = (((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__) 
                          << 0x18U) | (IData)((vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ 
                                               >> 0x20U)));
        __Vtemp_3[2U] = (((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__) 
                          >> 8U) | ((IData)((vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
                                             >> 0x20U)) 
                                    << 0x18U));
        __Vtemp_3[3U] = (((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__) 
                          << 0x10U) | ((IData)((vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
                                                >> 0x20U)) 
                                       >> 8U));
        __Vtemp_3[4U] = (((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__) 
                          >> 0x10U) | ((IData)((vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
                                                >> 0x20U)) 
                                       << 0x10U));
        __Vtemp_3[5U] = (((0xff00U & ((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__) 
                                      << 8U)) | ((IData)(
                                                         (vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
                                                          >> 0x20U)) 
                                                 >> 0x10U)) 
                         | (0xffff0000U & ((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__) 
                                           << 8U)));
        __Vtemp_3[6U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__) 
                           >> 0x18U) | (0xff00U & ((IData)(
                                                           (vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
                                                            >> 0x20U)) 
                                                   << 8U))) 
                         | (0xffff0000U & ((IData)(
                                                   (vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
                                                    >> 0x20U)) 
                                           << 8U)));
        bufp->chgWData(oldp+2,(__Vtemp_3),224);
        __Vtemp_6[0U] = (IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__);
        __Vtemp_6[1U] = (((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__) 
                          << 0x18U) | (IData)((vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ 
                                               >> 0x20U)));
        __Vtemp_6[2U] = (((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__) 
                          >> 8U) | ((IData)((vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
                                             >> 0x20U)) 
                                    << 0x18U));
        __Vtemp_6[3U] = (((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__) 
                          << 0x10U) | ((IData)((vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
                                                >> 0x20U)) 
                                       >> 8U));
        __Vtemp_6[4U] = (((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__) 
                          >> 0x10U) | ((IData)((vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
                                                >> 0x20U)) 
                                       << 0x10U));
        __Vtemp_6[5U] = (((0xff00U & ((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__) 
                                      << 8U)) | ((IData)(
                                                         (vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
                                                          >> 0x20U)) 
                                                 >> 0x10U)) 
                         | (0xffff0000U & ((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__) 
                                           << 8U)));
        __Vtemp_6[6U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__) 
                           >> 0x18U) | (0xff00U & ((IData)(
                                                           (vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
                                                            >> 0x20U)) 
                                                   << 8U))) 
                         | (0xffff0000U & ((IData)(
                                                   (vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
                                                    >> 0x20U)) 
                                           << 8U)));
        bufp->chgWData(oldp+9,(__Vtemp_6),224);
    }
    if (VL_UNLIKELY((vlSelfRef.__Vm_traceActivity[2U]))) {
        bufp->chgCData(oldp+16,(vlSelfRef.topSystolicArray__DOT__counter_q),4);
        bufp->chgBit(oldp+17,(vlSelfRef.topSystolicArray__DOT__validResult_q));
        bufp->chgBit(oldp+18,(vlSelfRef.topSystolicArray__DOT__doProcess_q));
        bufp->chgWData(oldp+19,(vlSelfRef.topSystolicArray__DOT__row_q),224);
        bufp->chgWData(oldp+26,(vlSelfRef.topSystolicArray__DOT__col_q),224);
        __Vtemp_9[0U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                           << 0x18U) | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                        << 0x10U)) 
                         | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                             << 8U) | (0xffU & vlSelfRef.topSystolicArray__DOT__row_q[0U])));
        __Vtemp_9[1U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                           << 0x18U) | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                        << 0x10U)) 
                         | ((0xff00U & (vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                                        >> 0x10U)) 
                            | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q)));
        __Vtemp_9[2U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                           << 0x18U) | (0xff0000U & 
                                        vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                         | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q) 
                             << 8U) | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q)));
        __Vtemp_9[3U] = (IData)((((QData)((IData)((
                                                   (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q) 
                                                     << 0x18U) 
                                                    | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                                       << 0x10U)) 
                                                   | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                                       << 8U) 
                                                      | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q))))) 
                                  << 0x20U) | (QData)((IData)(
                                                              (((0xff000000U 
                                                                 & (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                                                                    << 0x10U)) 
                                                                | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q) 
                                                                   << 0x10U)) 
                                                               | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                                                   << 8U) 
                                                                  | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q)))))));
        __Vtemp_9[4U] = (IData)(((((QData)((IData)(
                                                   ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q) 
                                                      << 0x18U) 
                                                     | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                                        << 0x10U)) 
                                                    | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                                        << 8U) 
                                                       | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q))))) 
                                   << 0x20U) | (QData)((IData)(
                                                               (((0xff000000U 
                                                                  & (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                                                                     << 0x10U)) 
                                                                 | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q) 
                                                                    << 0x10U)) 
                                                                | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                                                    << 8U) 
                                                                   | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q)))))) 
                                 >> 0x20U));
        bufp->chgWData(oldp+33,(__Vtemp_9),160);
        __Vtemp_12[0U] = (((0xff000000U & (vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                                           << 0x10U)) 
                           | (0xff0000U & vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                          | ((0xff00U & (vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                                         >> 0x10U)) 
                             | (0xffU & vlSelfRef.topSystolicArray__DOT__col_q[0U])));
        __Vtemp_12[1U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                            << 0x18U) | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                         << 0x10U)) 
                          | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                              << 8U) | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)));
        __Vtemp_12[2U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                            << 0x18U) | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                         << 0x10U)) 
                          | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                              << 8U) | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)));
        __Vtemp_12[3U] = (IData)((((QData)((IData)(
                                                   ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                                                      << 0x18U) 
                                                     | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                                        << 0x10U)) 
                                                    | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                                                        << 8U) 
                                                       | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q))))) 
                                   << 0x20U) | (QData)((IData)(
                                                               ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                                                                  << 0x18U) 
                                                                 | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                                                    << 0x10U)) 
                                                                | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                                                                    << 8U) 
                                                                   | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)))))));
        __Vtemp_12[4U] = (IData)(((((QData)((IData)(
                                                    ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                                                       << 0x18U) 
                                                      | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                                         << 0x10U)) 
                                                     | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                                                         << 8U) 
                                                        | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q))))) 
                                    << 0x20U) | (QData)((IData)(
                                                                ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                                                                   << 0x18U) 
                                                                  | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                                                     << 0x10U)) 
                                                                 | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                                                                     << 8U) 
                                                                    | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)))))) 
                                  >> 0x20U));
        bufp->chgWData(oldp+38,(__Vtemp_12),160);
        bufp->chgCData(oldp+43,((0xffU & vlSelfRef.topSystolicArray__DOT__row_q[0U])),8);
        bufp->chgCData(oldp+44,((0xffU & vlSelfRef.topSystolicArray__DOT__col_q[0U])),8);
        bufp->chgCData(oldp+45,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+46,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+47,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+48,(((0xffU & vlSelfRef.topSystolicArray__DOT__row_q[0U]) 
                                 * (0xffU & vlSelfRef.topSystolicArray__DOT__col_q[0U]))),32);
        bufp->chgIData(oldp+49,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                  ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
                                     + ((0xffU & vlSelfRef.topSystolicArray__DOT__row_q[0U]) 
                                        * (0xffU & 
                                           vlSelfRef.topSystolicArray__DOT__col_q[0U])))
                                  : 0U)),32);
        bufp->chgCData(oldp+50,((vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                                 >> 0x18U)),8);
        bufp->chgCData(oldp+51,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+52,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+53,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+54,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                 * (vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                                    >> 0x18U))),32);
        bufp->chgIData(oldp+55,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                  ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
                                     + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                        * (vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                                           >> 0x18U)))
                                  : 0U)),32);
        bufp->chgCData(oldp+56,((0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[3U] 
                                          >> 0x10U))),8);
        bufp->chgCData(oldp+57,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+58,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+59,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+60,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                 * (0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[3U] 
                                             >> 0x10U)))),32);
        bufp->chgIData(oldp+61,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                  ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
                                     + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                        * (0xffU & 
                                           (vlSelfRef.topSystolicArray__DOT__col_q[3U] 
                                            >> 0x10U))))
                                  : 0U)),32);
        bufp->chgCData(oldp+62,((0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                                          >> 8U))),8);
        bufp->chgCData(oldp+63,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+64,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+65,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+66,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                 * (0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                                             >> 8U)))),32);
        bufp->chgIData(oldp+67,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                  ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
                                     + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                        * (0xffU & 
                                           (vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                                            >> 8U))))
                                  : 0U)),32);
        bufp->chgCData(oldp+68,((vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                                 >> 0x18U)),8);
        bufp->chgCData(oldp+69,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+70,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+71,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+72,(((vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                                  >> 0x18U) * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+73,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                  ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
                                     + ((vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                                         >> 0x18U) 
                                        * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)))
                                  : 0U)),32);
        bufp->chgCData(oldp+74,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+75,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+76,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+77,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                 * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+78,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                  ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
                                     + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                        * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)))
                                  : 0U)),32);
        bufp->chgCData(oldp+79,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+80,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+81,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+82,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                 * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+83,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                  ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
                                     + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                        * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)))
                                  : 0U)),32);
        bufp->chgCData(oldp+84,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+85,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+86,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+87,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                 * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+88,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                  ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
                                     + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                        * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)))
                                  : 0U)),32);
        bufp->chgCData(oldp+89,((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[3U] 
                                          >> 0x10U))),8);
        bufp->chgCData(oldp+90,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+91,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+92,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+93,(((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[3U] 
                                           >> 0x10U)) 
                                 * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+94,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                  ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
                                     + ((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[3U] 
                                                  >> 0x10U)) 
                                        * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)))
                                  : 0U)),32);
        bufp->chgCData(oldp+95,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+96,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+97,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+98,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                 * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+99,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                  ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
                                     + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                        * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)))
                                  : 0U)),32);
        bufp->chgCData(oldp+100,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+101,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+102,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+103,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+104,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                   ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
                                      + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                         * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)))
                                   : 0U)),32);
        bufp->chgCData(oldp+105,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+106,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+107,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+108,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+109,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                   ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
                                      + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                         * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)))
                                   : 0U)),32);
        bufp->chgCData(oldp+110,((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                                           >> 8U))),8);
        bufp->chgCData(oldp+111,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+112,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+113,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+114,(((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                                            >> 8U)) 
                                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+115,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                   ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
                                      + ((0xffU & (
                                                   vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                                                   >> 8U)) 
                                         * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)))
                                   : 0U)),32);
        bufp->chgCData(oldp+116,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+117,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+118,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+119,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+120,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                   ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
                                      + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                         * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)))
                                   : 0U)),32);
        bufp->chgCData(oldp+121,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+122,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+123,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+124,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+125,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                   ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
                                      + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                         * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)))
                                   : 0U)),32);
        bufp->chgCData(oldp+126,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q),8);
        bufp->chgCData(oldp+127,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q),8);
        bufp->chgIData(oldp+128,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q),32);
        bufp->chgIData(oldp+129,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q))),32);
        bufp->chgIData(oldp+130,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                   ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
                                      + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                         * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)))
                                   : 0U)),32);
    }
    bufp->chgBit(oldp+131,(vlSelfRef.i_clk));
    bufp->chgBit(oldp+132,(vlSelfRef.i_arst));
    bufp->chgWData(oldp+133,(vlSelfRef.i_a),128);
    bufp->chgWData(oldp+137,(vlSelfRef.i_b),128);
    bufp->chgBit(oldp+141,(vlSelfRef.i_validInput));
    bufp->chgWData(oldp+142,(vlSelfRef.o_c),512);
    bufp->chgBit(oldp+158,(vlSelfRef.o_validResult));
    __Vtemp_14[0U] = vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_33;
    __Vtemp_14[1U] = (((vlSelfRef.i_a[1U] << 0x18U) 
                       | (0xff0000U & (vlSelfRef.i_a[1U] 
                                       << 8U))) | (
                                                   (0xff00U 
                                                    & (vlSelfRef.i_a[1U] 
                                                       >> 8U)) 
                                                   | (vlSelfRef.i_a[1U] 
                                                      >> 0x18U)));
    __Vtemp_14[2U] = (IData)((((QData)((IData)((((vlSelfRef.i_a[3U] 
                                                  << 0x18U) 
                                                 | (0xff0000U 
                                                    & (vlSelfRef.i_a[3U] 
                                                       << 8U))) 
                                                | ((0xff00U 
                                                    & (vlSelfRef.i_a[3U] 
                                                       >> 8U)) 
                                                   | (vlSelfRef.i_a[3U] 
                                                      >> 0x18U))))) 
                               << 0x20U) | (QData)((IData)(
                                                           (((vlSelfRef.i_a[2U] 
                                                              << 0x18U) 
                                                             | (0xff0000U 
                                                                & (vlSelfRef.i_a[2U] 
                                                                   << 8U))) 
                                                            | ((0xff00U 
                                                                & (vlSelfRef.i_a[2U] 
                                                                   >> 8U)) 
                                                               | (vlSelfRef.i_a[2U] 
                                                                  >> 0x18U)))))));
    __Vtemp_14[3U] = (IData)(((((QData)((IData)((((
                                                   vlSelfRef.i_a[3U] 
                                                   << 0x18U) 
                                                  | (0xff0000U 
                                                     & (vlSelfRef.i_a[3U] 
                                                        << 8U))) 
                                                 | ((0xff00U 
                                                     & (vlSelfRef.i_a[3U] 
                                                        >> 8U)) 
                                                    | (vlSelfRef.i_a[3U] 
                                                       >> 0x18U))))) 
                                << 0x20U) | (QData)((IData)(
                                                            (((vlSelfRef.i_a[2U] 
                                                               << 0x18U) 
                                                              | (0xff0000U 
                                                                 & (vlSelfRef.i_a[2U] 
                                                                    << 8U))) 
                                                             | ((0xff00U 
                                                                 & (vlSelfRef.i_a[2U] 
                                                                    >> 8U)) 
                                                                | (vlSelfRef.i_a[2U] 
                                                                   >> 0x18U)))))) 
                              >> 0x20U));
    bufp->chgWData(oldp+159,(__Vtemp_14),128);
    __Vtemp_16[0U] = vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_34;
    __Vtemp_16[1U] = (((0xff000000U & (vlSelfRef.i_b[0U] 
                                       << 0x10U)) | 
                       (0xff0000U & (vlSelfRef.i_b[1U] 
                                     << 8U))) | ((0xff00U 
                                                  & vlSelfRef.i_b[2U]) 
                                                 | (0xffU 
                                                    & (vlSelfRef.i_b[3U] 
                                                       >> 8U))));
    __Vtemp_16[2U] = (IData)((((QData)((IData)((((0xff000000U 
                                                  & vlSelfRef.i_b[0U]) 
                                                 | (0xff0000U 
                                                    & (vlSelfRef.i_b[1U] 
                                                       >> 8U))) 
                                                | ((0xff00U 
                                                    & (vlSelfRef.i_b[2U] 
                                                       >> 0x10U)) 
                                                   | (vlSelfRef.i_b[3U] 
                                                      >> 0x18U))))) 
                               << 0x20U) | (QData)((IData)(
                                                           (((0xff000000U 
                                                              & (vlSelfRef.i_b[0U] 
                                                                 << 8U)) 
                                                             | (0xff0000U 
                                                                & vlSelfRef.i_b[1U])) 
                                                            | ((0xff00U 
                                                                & (vlSelfRef.i_b[2U] 
                                                                   >> 8U)) 
                                                               | (0xffU 
                                                                  & (vlSelfRef.i_b[3U] 
                                                                     >> 0x10U))))))));
    __Vtemp_16[3U] = (IData)(((((QData)((IData)((((0xff000000U 
                                                   & vlSelfRef.i_b[0U]) 
                                                  | (0xff0000U 
                                                     & (vlSelfRef.i_b[1U] 
                                                        >> 8U))) 
                                                 | ((0xff00U 
                                                     & (vlSelfRef.i_b[2U] 
                                                        >> 0x10U)) 
                                                    | (vlSelfRef.i_b[3U] 
                                                       >> 0x18U))))) 
                                << 0x20U) | (QData)((IData)(
                                                            (((0xff000000U 
                                                               & (vlSelfRef.i_b[0U] 
                                                                  << 8U)) 
                                                              | (0xff0000U 
                                                                 & vlSelfRef.i_b[1U])) 
                                                             | ((0xff00U 
                                                                 & (vlSelfRef.i_b[2U] 
                                                                    >> 8U)) 
                                                                | (0xffU 
                                                                   & (vlSelfRef.i_b[3U] 
                                                                      >> 0x10U))))))) 
                              >> 0x20U));
    bufp->chgWData(oldp+163,(__Vtemp_16),128);
}

void VtopSystolicArray___024root__trace_cleanup(void* voidSelf, VerilatedVcd* /*unused*/) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root__trace_cleanup\n"); );
    // Init
    VtopSystolicArray___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<VtopSystolicArray___024root*>(voidSelf);
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    // Body
    vlSymsp->__Vm_activity = false;
    vlSymsp->TOP.__Vm_traceActivity[0U] = 0U;
    vlSymsp->TOP.__Vm_traceActivity[1U] = 0U;
    vlSymsp->TOP.__Vm_traceActivity[2U] = 0U;
}
