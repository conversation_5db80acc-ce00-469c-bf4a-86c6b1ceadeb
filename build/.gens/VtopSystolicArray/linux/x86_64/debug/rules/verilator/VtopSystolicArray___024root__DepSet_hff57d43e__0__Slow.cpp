// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See VtopSystolicArray.h for the primary calling header

#include "VtopSystolicArray__pch.h"
#include "VtopSystolicArray___024root.h"

VL_ATTR_COLD void VtopSystolicArray___024root___eval_static(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_static\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelfRef.__Vtrigprevexpr___TOP__i_arst__0 = vlSelfRef.i_arst;
    vlSelfRef.__Vtrigprevexpr___TOP__i_clk__0 = vlSelfRef.i_clk;
}

VL_ATTR_COLD void VtopSystolicArray___024root___eval_initial(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_initial\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
}

VL_ATTR_COLD void VtopSystolicArray___024root___eval_final(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_final\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
}

#ifdef VL_DEBUG
VL_ATTR_COLD void VtopSystolicArray___024root___dump_triggers__stl(VtopSystolicArray___024root* vlSelf);
#endif  // VL_DEBUG
VL_ATTR_COLD bool VtopSystolicArray___024root___eval_phase__stl(VtopSystolicArray___024root* vlSelf);

VL_ATTR_COLD void VtopSystolicArray___024root___eval_settle(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_settle\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    IData/*31:0*/ __VstlIterCount;
    CData/*0:0*/ __VstlContinue;
    // Body
    __VstlIterCount = 0U;
    vlSelfRef.__VstlFirstIteration = 1U;
    __VstlContinue = 1U;
    while (__VstlContinue) {
        if (VL_UNLIKELY(((0x64U < __VstlIterCount)))) {
#ifdef VL_DEBUG
            VtopSystolicArray___024root___dump_triggers__stl(vlSelf);
#endif
            VL_FATAL_MT("rtl/topSystolicArray.sv", 3, "", "Settle region did not converge.");
        }
        __VstlIterCount = ((IData)(1U) + __VstlIterCount);
        __VstlContinue = 0U;
        if (VtopSystolicArray___024root___eval_phase__stl(vlSelf)) {
            __VstlContinue = 1U;
        }
        vlSelfRef.__VstlFirstIteration = 0U;
    }
}

#ifdef VL_DEBUG
VL_ATTR_COLD void VtopSystolicArray___024root___dump_triggers__stl(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___dump_triggers__stl\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1U & (~ vlSelfRef.__VstlTriggered.any()))) {
        VL_DBG_MSGF("         No triggers active\n");
    }
    if ((1ULL & vlSelfRef.__VstlTriggered.word(0U))) {
        VL_DBG_MSGF("         'stl' region trigger index 0 is active: Internal 'stl' trigger - first iteration\n");
    }
}
#endif  // VL_DEBUG

VL_ATTR_COLD void VtopSystolicArray___024root___stl_sequent__TOP__0(VtopSystolicArray___024root* vlSelf);
VL_ATTR_COLD void VtopSystolicArray___024root____Vm_traceActivitySetAll(VtopSystolicArray___024root* vlSelf);

VL_ATTR_COLD void VtopSystolicArray___024root___eval_stl(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_stl\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1ULL & vlSelfRef.__VstlTriggered.word(0U))) {
        VtopSystolicArray___024root___stl_sequent__TOP__0(vlSelf);
        VtopSystolicArray___024root____Vm_traceActivitySetAll(vlSelf);
    }
}

VL_ATTR_COLD void VtopSystolicArray___024root___stl_sequent__TOP__0(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___stl_sequent__TOP__0\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelfRef.o_validResult = vlSelfRef.topSystolicArray__DOT__validResult_q;
    if (vlSelfRef.topSystolicArray__DOT__doProcess_q) {
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
               + ((0xffU & vlSelfRef.topSystolicArray__DOT__row_q[0U]) 
                  * (0xffU & vlSelfRef.topSystolicArray__DOT__col_q[0U])));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                  * (vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                     >> 0x18U)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                  * (0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[3U] 
                              >> 0x10U))));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                  * (0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                              >> 8U))));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
               + ((vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                   >> 0x18U) * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
               + ((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[3U] 
                            >> 0x10U)) * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
               + ((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                            >> 8U)) * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)));
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d 
            = (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
               + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                  * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)));
    } else {
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = 0U;
        vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = 0U;
    }
    vlSelfRef.topSystolicArray__DOT__doProcess_d = 
        ((IData)(vlSelfRef.i_validInput) | ((0xbU != (IData)(vlSelfRef.topSystolicArray__DOT__counter_q)) 
                                            & (IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)));
    vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_33 
        = (((vlSelfRef.i_a[0U] << 0x18U) | (0xff0000U 
                                            & (vlSelfRef.i_a[0U] 
                                               << 8U))) 
           | ((0xff00U & (vlSelfRef.i_a[0U] >> 8U)) 
              | (vlSelfRef.i_a[0U] >> 0x18U)));
    vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_34 
        = (((vlSelfRef.i_b[0U] << 0x18U) | (0xff0000U 
                                            & (vlSelfRef.i_b[1U] 
                                               << 0x10U))) 
           | ((0xff00U & (vlSelfRef.i_b[2U] << 8U)) 
              | (0xffU & vlSelfRef.i_b[3U])));
    if (vlSelfRef.i_validInput) {
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & vlSelfRef.i_a[1U]))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_a[1U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_a[1U] 
                                                                              >> 8U)) 
                                                                          | (vlSelfRef.i_a[1U] 
                                                                             >> 0x18U)))))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & (vlSelfRef.i_b[0U] 
                                                                         >> 8U)))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_b[1U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & vlSelfRef.i_b[2U]) 
                                                                          | (0xffU 
                                                                             & (vlSelfRef.i_b[3U] 
                                                                                >> 8U))))))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & vlSelfRef.i_a[2U]))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_a[2U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_a[2U] 
                                                                              >> 8U)) 
                                                                          | (vlSelfRef.i_a[2U] 
                                                                             >> 0x18U)))))), 0x10U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & (vlSelfRef.i_b[0U] 
                                                                         >> 0x10U)))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & vlSelfRef.i_b[1U]) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_b[2U] 
                                                                              >> 8U)) 
                                                                          | (0xffU 
                                                                             & (vlSelfRef.i_b[3U] 
                                                                                >> 0x10U))))))), 0x10U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (0xffU 
                                                                      & vlSelfRef.i_a[3U]))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_a[3U] 
                                                                           << 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_a[3U] 
                                                                              >> 8U)) 
                                                                          | (vlSelfRef.i_a[3U] 
                                                                             >> 0x18U)))))), 0x18U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTL_QQI(56,56,32, 
                                                   (((QData)((IData)(
                                                                     (vlSelfRef.i_b[0U] 
                                                                      >> 0x18U))) 
                                                     << 0x18U) 
                                                    | (QData)((IData)(
                                                                      ((0xff0000U 
                                                                        & (vlSelfRef.i_b[1U] 
                                                                           >> 8U)) 
                                                                       | ((0xff00U 
                                                                           & (vlSelfRef.i_b[2U] 
                                                                              >> 0x10U)) 
                                                                          | (vlSelfRef.i_b[3U] 
                                                                             >> 0x18U)))))), 0x18U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (QData)((IData)(vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_33)));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (QData)((IData)(vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_34)));
    } else if ((0U != (IData)(vlSelfRef.topSystolicArray__DOT__counter_q))) {
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                                        << 0x28U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__row_q[2U])) 
                                                           << 8U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                                             >> 0x18U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                                        << 0x28U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__col_q[2U])) 
                                                           << 8U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                                             >> 0x18U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                                        << 0x30U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__row_q[4U])) 
                                                           << 0x10U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                                             >> 0x10U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                                        << 0x30U) 
                                                       | (((QData)((IData)(
                                                                           vlSelfRef.topSystolicArray__DOT__col_q[4U])) 
                                                           << 0x10U) 
                                                          | ((QData)((IData)(
                                                                             vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                                             >> 0x10U)))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[6U])) 
                                                        << 0x18U) 
                                                       | ((QData)((IData)(
                                                                          vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                                          >> 8U))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[6U])) 
                                                        << 0x18U) 
                                                       | ((QData)((IData)(
                                                                          vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                                          >> 8U))), 8U));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                                        << 0x20U) 
                                                       | (QData)((IData)(
                                                                         vlSelfRef.topSystolicArray__DOT__row_q[0U])))), 8U));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & VL_SHIFTR_QQI(56,56,32, 
                                                   (0xffffffffffffffULL 
                                                    & (((QData)((IData)(
                                                                        vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                                        << 0x20U) 
                                                       | (QData)((IData)(
                                                                         vlSelfRef.topSystolicArray__DOT__col_q[0U])))), 8U));
    } else {
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                       << 0x28U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[2U])) 
                                        << 8U) | ((QData)((IData)(
                                                                  vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                                  >> 0x18U))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                       << 0x28U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[2U])) 
                                        << 8U) | ((QData)((IData)(
                                                                  vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                                  >> 0x18U))));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                       << 0x30U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[4U])) 
                                        << 0x10U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                                        >> 0x10U))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                       << 0x30U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[4U])) 
                                        << 0x10U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                                        >> 0x10U))));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[6U])) 
                                       << 0x38U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[6U])) 
                                        << 0x18U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__row_q[5U])) 
                                        >> 8U))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[6U])) 
                                       << 0x38U) | 
                                      (((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[6U])) 
                                        << 0x18U) | 
                                       ((QData)((IData)(
                                                        vlSelfRef.topSystolicArray__DOT__col_q[5U])) 
                                        >> 8U))));
        vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__row_q[1U])) 
                                       << 0x20U) | (QData)((IData)(
                                                                   vlSelfRef.topSystolicArray__DOT__row_q[0U]))));
        vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ 
            = (0xffffffffffffffULL & (((QData)((IData)(
                                                       vlSelfRef.topSystolicArray__DOT__col_q[1U])) 
                                       << 0x20U) | (QData)((IData)(
                                                                   vlSelfRef.topSystolicArray__DOT__col_q[0U]))));
    }
    vlSelfRef.o_c[0U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[1U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[2U] = (IData)((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                  << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))));
    vlSelfRef.o_c[3U] = (IData)(((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                   << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))) 
                                 >> 0x20U));
    vlSelfRef.o_c[4U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[5U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[6U] = (IData)((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                  << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))));
    vlSelfRef.o_c[7U] = (IData)(((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                   << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))) 
                                 >> 0x20U));
    vlSelfRef.o_c[8U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[9U] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[0xaU] = (IData)((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                    << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))));
    vlSelfRef.o_c[0xbU] = (IData)(((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                     << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))) 
                                   >> 0x20U));
    vlSelfRef.o_c[0xcU] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[0xdU] = vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q;
    vlSelfRef.o_c[0xeU] = (IData)((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                    << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))));
    vlSelfRef.o_c[0xfU] = (IData)(((((QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q)) 
                                     << 0x20U) | (QData)((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q))) 
                                   >> 0x20U));
    vlSelfRef.topSystolicArray__DOT__counter_d = ((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_d)
                                                   ? 
                                                  (0xfU 
                                                   & ((IData)(1U) 
                                                      + (IData)(vlSelfRef.topSystolicArray__DOT__counter_q)))
                                                   : 0U);
}

VL_ATTR_COLD void VtopSystolicArray___024root___eval_triggers__stl(VtopSystolicArray___024root* vlSelf);

VL_ATTR_COLD bool VtopSystolicArray___024root___eval_phase__stl(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_phase__stl\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    CData/*0:0*/ __VstlExecute;
    // Body
    VtopSystolicArray___024root___eval_triggers__stl(vlSelf);
    __VstlExecute = vlSelfRef.__VstlTriggered.any();
    if (__VstlExecute) {
        VtopSystolicArray___024root___eval_stl(vlSelf);
    }
    return (__VstlExecute);
}

#ifdef VL_DEBUG
VL_ATTR_COLD void VtopSystolicArray___024root___dump_triggers__ico(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___dump_triggers__ico\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1U & (~ vlSelfRef.__VicoTriggered.any()))) {
        VL_DBG_MSGF("         No triggers active\n");
    }
    if ((1ULL & vlSelfRef.__VicoTriggered.word(0U))) {
        VL_DBG_MSGF("         'ico' region trigger index 0 is active: Internal 'ico' trigger - first iteration\n");
    }
}
#endif  // VL_DEBUG

#ifdef VL_DEBUG
VL_ATTR_COLD void VtopSystolicArray___024root___dump_triggers__act(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___dump_triggers__act\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1U & (~ vlSelfRef.__VactTriggered.any()))) {
        VL_DBG_MSGF("         No triggers active\n");
    }
    if ((1ULL & vlSelfRef.__VactTriggered.word(0U))) {
        VL_DBG_MSGF("         'act' region trigger index 0 is active: @(posedge i_arst)\n");
    }
    if ((2ULL & vlSelfRef.__VactTriggered.word(0U))) {
        VL_DBG_MSGF("         'act' region trigger index 1 is active: @(posedge i_clk)\n");
    }
}
#endif  // VL_DEBUG

#ifdef VL_DEBUG
VL_ATTR_COLD void VtopSystolicArray___024root___dump_triggers__nba(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___dump_triggers__nba\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1U & (~ vlSelfRef.__VnbaTriggered.any()))) {
        VL_DBG_MSGF("         No triggers active\n");
    }
    if ((1ULL & vlSelfRef.__VnbaTriggered.word(0U))) {
        VL_DBG_MSGF("         'nba' region trigger index 0 is active: @(posedge i_arst)\n");
    }
    if ((2ULL & vlSelfRef.__VnbaTriggered.word(0U))) {
        VL_DBG_MSGF("         'nba' region trigger index 1 is active: @(posedge i_clk)\n");
    }
}
#endif  // VL_DEBUG

VL_ATTR_COLD void VtopSystolicArray___024root____Vm_traceActivitySetAll(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root____Vm_traceActivitySetAll\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelfRef.__Vm_traceActivity[0U] = 1U;
    vlSelfRef.__Vm_traceActivity[1U] = 1U;
    vlSelfRef.__Vm_traceActivity[2U] = 1U;
}

VL_ATTR_COLD void VtopSystolicArray___024root___ctor_var_reset(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___ctor_var_reset\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelf->i_clk = VL_RAND_RESET_I(1);
    vlSelf->i_arst = VL_RAND_RESET_I(1);
    VL_RAND_RESET_W(128, vlSelf->i_a);
    VL_RAND_RESET_W(128, vlSelf->i_b);
    vlSelf->i_validInput = VL_RAND_RESET_I(1);
    VL_RAND_RESET_W(512, vlSelf->o_c);
    vlSelf->o_validResult = VL_RAND_RESET_I(1);
    vlSelf->topSystolicArray__DOT__counter_d = VL_RAND_RESET_I(4);
    vlSelf->topSystolicArray__DOT__counter_q = VL_RAND_RESET_I(4);
    vlSelf->topSystolicArray__DOT__validResult_q = VL_RAND_RESET_I(1);
    vlSelf->topSystolicArray__DOT__doProcess_d = VL_RAND_RESET_I(1);
    vlSelf->topSystolicArray__DOT__doProcess_q = VL_RAND_RESET_I(1);
    vlSelf->topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ = VL_RAND_RESET_Q(56);
    vlSelf->topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ = VL_RAND_RESET_Q(56);
    vlSelf->topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ = VL_RAND_RESET_Q(56);
    vlSelf->topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ = VL_RAND_RESET_Q(56);
    VL_RAND_RESET_W(224, vlSelf->topSystolicArray__DOT__row_q);
    vlSelf->topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ = VL_RAND_RESET_Q(56);
    vlSelf->topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ = VL_RAND_RESET_Q(56);
    vlSelf->topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ = VL_RAND_RESET_Q(56);
    vlSelf->topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ = VL_RAND_RESET_Q(56);
    VL_RAND_RESET_W(224, vlSelf->topSystolicArray__DOT__col_q);
    vlSelf->topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_33 = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_34 = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_d = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q = VL_RAND_RESET_I(32);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q = VL_RAND_RESET_I(8);
    vlSelf->topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q = VL_RAND_RESET_I(8);
    vlSelf->__Vtrigprevexpr___TOP__i_arst__0 = VL_RAND_RESET_I(1);
    vlSelf->__Vtrigprevexpr___TOP__i_clk__0 = VL_RAND_RESET_I(1);
    for (int __Vi0 = 0; __Vi0 < 3; ++__Vi0) {
        vlSelf->__Vm_traceActivity[__Vi0] = 0;
    }
}
