// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Model implementation (design independent parts)

#include "VtopSystolicArray__pch.h"
#include "verilated_vcd_c.h"

//============================================================
// Constructors

VtopSystolicArray::VtopSystolicArray(VerilatedContext* _vcontextp__, const char* _vcname__)
    : VerilatedModel{*_vcontextp__}
    , vlSymsp{new VtopSystolicArray__Syms(contextp(), _vcname__, this)}
    , i_clk{vlSymsp->TOP.i_clk}
    , i_arst{vlSymsp->TOP.i_arst}
    , i_a{vlSymsp->TOP.i_a}
    , i_b{vlSymsp->TOP.i_b}
    , i_validInput{vlSymsp->TOP.i_validInput}
    , o_validResult{vlSymsp->TOP.o_validResult}
    , o_c{vlSymsp->TOP.o_c}
    , rootp{&(vlSymsp->TOP)}
{
    // Register model with the context
    contextp()->addModel(this);
    contextp()->traceBaseModelCbAdd(
        [this](VerilatedTraceBaseC* tfp, int levels, int options) { traceBaseModel(tfp, levels, options); });
}

VtopSystolicArray::VtopSystolicArray(const char* _vcname__)
    : VtopSystolicArray(Verilated::threadContextp(), _vcname__)
{
}

//============================================================
// Destructor

VtopSystolicArray::~VtopSystolicArray() {
    delete vlSymsp;
}

//============================================================
// Evaluation function

#ifdef VL_DEBUG
void VtopSystolicArray___024root___eval_debug_assertions(VtopSystolicArray___024root* vlSelf);
#endif  // VL_DEBUG
void VtopSystolicArray___024root___eval_static(VtopSystolicArray___024root* vlSelf);
void VtopSystolicArray___024root___eval_initial(VtopSystolicArray___024root* vlSelf);
void VtopSystolicArray___024root___eval_settle(VtopSystolicArray___024root* vlSelf);
void VtopSystolicArray___024root___eval(VtopSystolicArray___024root* vlSelf);

void VtopSystolicArray::eval_step() {
    VL_DEBUG_IF(VL_DBG_MSGF("+++++TOP Evaluate VtopSystolicArray::eval_step\n"); );
#ifdef VL_DEBUG
    // Debug assertions
    VtopSystolicArray___024root___eval_debug_assertions(&(vlSymsp->TOP));
#endif  // VL_DEBUG
    vlSymsp->__Vm_activity = true;
    vlSymsp->__Vm_deleter.deleteAll();
    if (VL_UNLIKELY(!vlSymsp->__Vm_didInit)) {
        vlSymsp->__Vm_didInit = true;
        VL_DEBUG_IF(VL_DBG_MSGF("+ Initial\n"););
        VtopSystolicArray___024root___eval_static(&(vlSymsp->TOP));
        VtopSystolicArray___024root___eval_initial(&(vlSymsp->TOP));
        VtopSystolicArray___024root___eval_settle(&(vlSymsp->TOP));
    }
    VL_DEBUG_IF(VL_DBG_MSGF("+ Eval\n"););
    VtopSystolicArray___024root___eval(&(vlSymsp->TOP));
    // Evaluate cleanup
    Verilated::endOfEval(vlSymsp->__Vm_evalMsgQp);
}

//============================================================
// Events and timing
bool VtopSystolicArray::eventsPending() { return false; }

uint64_t VtopSystolicArray::nextTimeSlot() {
    VL_FATAL_MT(__FILE__, __LINE__, "", "No delays in the design");
    return 0;
}

//============================================================
// Utilities

const char* VtopSystolicArray::name() const {
    return vlSymsp->name();
}

//============================================================
// Invoke final blocks

void VtopSystolicArray___024root___eval_final(VtopSystolicArray___024root* vlSelf);

VL_ATTR_COLD void VtopSystolicArray::final() {
    VtopSystolicArray___024root___eval_final(&(vlSymsp->TOP));
}

//============================================================
// Implementations of abstract methods from VerilatedModel

const char* VtopSystolicArray::hierName() const { return vlSymsp->name(); }
const char* VtopSystolicArray::modelName() const { return "VtopSystolicArray"; }
unsigned VtopSystolicArray::threads() const { return 1; }
void VtopSystolicArray::prepareClone() const { contextp()->prepareClone(); }
void VtopSystolicArray::atClone() const {
    contextp()->threadPoolpOnClone();
}
std::unique_ptr<VerilatedTraceConfig> VtopSystolicArray::traceConfig() const {
    return std::unique_ptr<VerilatedTraceConfig>{new VerilatedTraceConfig{false, false, false}};
};

//============================================================
// Trace configuration

void VtopSystolicArray___024root__trace_decl_types(VerilatedVcd* tracep);

void VtopSystolicArray___024root__trace_init_top(VtopSystolicArray___024root* vlSelf, VerilatedVcd* tracep);

VL_ATTR_COLD static void trace_init(void* voidSelf, VerilatedVcd* tracep, uint32_t code) {
    // Callback from tracep->open()
    VtopSystolicArray___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<VtopSystolicArray___024root*>(voidSelf);
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    if (!vlSymsp->_vm_contextp__->calcUnusedSigs()) {
        VL_FATAL_MT(__FILE__, __LINE__, __FILE__,
            "Turning on wave traces requires Verilated::traceEverOn(true) call before time 0.");
    }
    vlSymsp->__Vm_baseCode = code;
    tracep->pushPrefix(std::string{vlSymsp->name()}, VerilatedTracePrefixType::SCOPE_MODULE);
    VtopSystolicArray___024root__trace_decl_types(tracep);
    VtopSystolicArray___024root__trace_init_top(vlSelf, tracep);
    tracep->popPrefix();
}

VL_ATTR_COLD void VtopSystolicArray___024root__trace_register(VtopSystolicArray___024root* vlSelf, VerilatedVcd* tracep);

VL_ATTR_COLD void VtopSystolicArray::traceBaseModel(VerilatedTraceBaseC* tfp, int levels, int options) {
    (void)levels; (void)options;
    VerilatedVcdC* const stfp = dynamic_cast<VerilatedVcdC*>(tfp);
    if (VL_UNLIKELY(!stfp)) {
        vl_fatal(__FILE__, __LINE__, __FILE__,"'VtopSystolicArray::trace()' called on non-VerilatedVcdC object;"
            " use --trace-fst with VerilatedFst object, and --trace-vcd with VerilatedVcd object");
    }
    stfp->spTrace()->addModel(this);
    stfp->spTrace()->addInitCb(&trace_init, &(vlSymsp->TOP));
    VtopSystolicArray___024root__trace_register(&(vlSymsp->TOP), stfp->spTrace());
}
