// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See VtopSystolicArray.h for the primary calling header

#include "VtopSystolicArray__pch.h"
#include "VtopSystolicArray__Syms.h"
#include "VtopSystolicArray___024root.h"

#ifdef VL_DEBUG
VL_ATTR_COLD void VtopSystolicArray___024root___dump_triggers__ico(VtopSystolicArray___024root* vlSelf);
#endif  // VL_DEBUG

void VtopSystolicArray___024root___eval_triggers__ico(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_triggers__ico\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelfRef.__VicoTriggered.setBit(0U, (IData)(vlSelfRef.__VicoFirstIteration));
#ifdef VL_DEBUG
    if (VL_UNLIKELY(vlSymsp->_vm_contextp__->debug())) {
        VtopSystolicArray___024root___dump_triggers__ico(vlSelf);
    }
#endif
}

#ifdef VL_DEBUG
VL_ATTR_COLD void VtopSystolicArray___024root___dump_triggers__act(VtopSystolicArray___024root* vlSelf);
#endif  // VL_DEBUG

void VtopSystolicArray___024root___eval_triggers__act(VtopSystolicArray___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root___eval_triggers__act\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelfRef.__VactTriggered.setBit(0U, ((IData)(vlSelfRef.i_arst) 
                                          & (~ (IData)(vlSelfRef.__Vtrigprevexpr___TOP__i_arst__0))));
    vlSelfRef.__VactTriggered.setBit(1U, ((IData)(vlSelfRef.i_clk) 
                                          & (~ (IData)(vlSelfRef.__Vtrigprevexpr___TOP__i_clk__0))));
    vlSelfRef.__Vtrigprevexpr___TOP__i_arst__0 = vlSelfRef.i_arst;
    vlSelfRef.__Vtrigprevexpr___TOP__i_clk__0 = vlSelfRef.i_clk;
#ifdef VL_DEBUG
    if (VL_UNLIKELY(vlSymsp->_vm_contextp__->debug())) {
        VtopSystolicArray___024root___dump_triggers__act(vlSelf);
    }
#endif
}
