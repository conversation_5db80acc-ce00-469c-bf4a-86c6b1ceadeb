// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Tracing implementation internals
#include "verilated_vcd_c.h"
#include "VtopSystolicArray__Syms.h"


VL_ATTR_COLD void VtopSystolicArray___024root__trace_init_sub__TOP__0(VtopSystolicArray___024root* vlSelf, VerilatedVcd* tracep) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root__trace_init_sub__TOP__0\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    const int c = vlSymsp->__Vm_baseCode;
    // Body
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declArray(c+134,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 127,0);
    tracep->declArray(c+138,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 127,0);
    tracep->declBit(c+142,0,"i_validInput",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declArray(c+143,0,"o_c",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 511,0);
    tracep->declBit(c+159,0,"o_validResult",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->pushPrefix("topSystolicArray", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBus(c+168,0,"N",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::PARAMETER, VerilatedTraceSigType::INT, false,-1, 31,0);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declArray(c+134,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 127,0);
    tracep->declArray(c+138,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 127,0);
    tracep->declBit(c+142,0,"i_validInput",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declArray(c+143,0,"o_c",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 511,0);
    tracep->declBit(c+159,0,"o_validResult",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+169,0,"N_VALID",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::PARAMETER, VerilatedTraceSigType::BIT, false,-1);
    tracep->declBus(c+170,0,"MULT_CYCLES",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::PARAMETER, VerilatedTraceSigType::INT, false,-1, 31,0);
    tracep->declBus(c+168,0,"MULT_CYCLES_W",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::PARAMETER, VerilatedTraceSigType::INT, false,-1, 31,0);
    tracep->declBus(c+1,0,"counter_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 3,0);
    tracep->declBus(c+17,0,"counter_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 3,0);
    tracep->declBit(c+18,0,"validResult_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+2,0,"doProcess_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"doProcess_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+171,0,"PAD",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::PARAMETER, VerilatedTraceSigType::INT, false,-1, 31,0);
    tracep->declBus(c+172,0,"APPEND_ZERO",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::PARAMETER, VerilatedTraceSigType::BIT, false,-1, 23,0);
    tracep->declArray(c+3,0,"row_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 223,0);
    tracep->declArray(c+20,0,"row_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 223,0);
    tracep->declArray(c+10,0,"col_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 223,0);
    tracep->declArray(c+27,0,"col_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 223,0);
    tracep->declArray(c+160,0,"invertedRowElements",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 127,0);
    tracep->declArray(c+164,0,"invertedColElements",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 127,0);
    tracep->pushPrefix("u_systolicArray", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBus(c+168,0,"N",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::PARAMETER, VerilatedTraceSigType::INT, false,-1, 31,0);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declArray(c+20,0,"i_row",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 223,0);
    tracep->declArray(c+27,0,"i_col",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 223,0);
    tracep->declArray(c+143,0,"o_c",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 511,0);
    tracep->declArray(c+34,0,"rowInterConnect",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 159,0);
    tracep->declArray(c+39,0,"colInterConnect",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 159,0);
    tracep->pushPrefix("PerRow[0]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("PerCol[0]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+44,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+45,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+46,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+47,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+48,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+49,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+50,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+48,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+46,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+47,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[1]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+46,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+51,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+52,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+53,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+54,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+55,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+56,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+54,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+52,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+53,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[2]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+52,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+57,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+58,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+59,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+60,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+61,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+62,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+60,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+58,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+59,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[3]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+58,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+63,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+64,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+65,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+66,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+67,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+68,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+66,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+64,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+65,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerRow[1]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("PerCol[0]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+69,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+47,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+70,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+71,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+72,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+73,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+74,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+72,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+70,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+71,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[1]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+70,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+53,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+75,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+76,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+77,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+78,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+79,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+77,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+75,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+76,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[2]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+75,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+59,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+80,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+81,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+82,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+83,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+84,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+82,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+80,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+81,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[3]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+80,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+65,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+85,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+86,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+87,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+88,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+89,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+87,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+85,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+86,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerRow[2]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("PerCol[0]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+90,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+71,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+91,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+92,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+93,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+94,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+95,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+93,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+91,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+92,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[1]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+91,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+76,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+96,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+97,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+98,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+99,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+100,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+98,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+96,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+97,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[2]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+96,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+81,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+101,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+102,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+103,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+104,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+105,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+103,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+101,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+102,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[3]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+101,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+86,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+106,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+107,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+108,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+109,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+110,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+108,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+106,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+107,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerRow[3]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("PerCol[0]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+111,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+92,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+112,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+113,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+114,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+115,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+116,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+114,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+112,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+113,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[1]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+112,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+97,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+117,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+118,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+119,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+120,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+121,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+119,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+117,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+118,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[2]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+117,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+102,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+122,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+123,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+124,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+125,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+126,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+124,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+122,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+123,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->pushPrefix("PerCol[3]", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->pushPrefix("u_pe", VerilatedTracePrefixType::SCOPE_MODULE);
    tracep->declBit(c+132,0,"i_clk",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+133,0,"i_arst",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBit(c+19,0,"i_doProcess",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1);
    tracep->declBus(c+122,0,"i_a",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+107,0,"i_b",-1, VerilatedTraceSigDirection::INPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+127,0,"o_a",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+128,0,"o_b",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+129,0,"o_y",-1, VerilatedTraceSigDirection::OUTPUT, VerilatedTraceSigKind::WIRE, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+130,0,"mult",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+131,0,"mac_d",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+129,0,"mac_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 31,0);
    tracep->declBus(c+127,0,"a_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->declBus(c+128,0,"b_q",-1, VerilatedTraceSigDirection::NONE, VerilatedTraceSigKind::VAR, VerilatedTraceSigType::LOGIC, false,-1, 7,0);
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->popPrefix();
    tracep->popPrefix();
}

VL_ATTR_COLD void VtopSystolicArray___024root__trace_init_top(VtopSystolicArray___024root* vlSelf, VerilatedVcd* tracep) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root__trace_init_top\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    VtopSystolicArray___024root__trace_init_sub__TOP__0(vlSelf, tracep);
}

VL_ATTR_COLD void VtopSystolicArray___024root__trace_const_0(void* voidSelf, VerilatedVcd::Buffer* bufp);
VL_ATTR_COLD void VtopSystolicArray___024root__trace_full_0(void* voidSelf, VerilatedVcd::Buffer* bufp);
void VtopSystolicArray___024root__trace_chg_0(void* voidSelf, VerilatedVcd::Buffer* bufp);
void VtopSystolicArray___024root__trace_cleanup(void* voidSelf, VerilatedVcd* /*unused*/);

VL_ATTR_COLD void VtopSystolicArray___024root__trace_register(VtopSystolicArray___024root* vlSelf, VerilatedVcd* tracep) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root__trace_register\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    tracep->addConstCb(&VtopSystolicArray___024root__trace_const_0, 0U, vlSelf);
    tracep->addFullCb(&VtopSystolicArray___024root__trace_full_0, 0U, vlSelf);
    tracep->addChgCb(&VtopSystolicArray___024root__trace_chg_0, 0U, vlSelf);
    tracep->addCleanupCb(&VtopSystolicArray___024root__trace_cleanup, vlSelf);
}

VL_ATTR_COLD void VtopSystolicArray___024root__trace_const_0_sub_0(VtopSystolicArray___024root* vlSelf, VerilatedVcd::Buffer* bufp);

VL_ATTR_COLD void VtopSystolicArray___024root__trace_const_0(void* voidSelf, VerilatedVcd::Buffer* bufp) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root__trace_const_0\n"); );
    // Init
    VtopSystolicArray___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<VtopSystolicArray___024root*>(voidSelf);
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    // Body
    VtopSystolicArray___024root__trace_const_0_sub_0((&vlSymsp->TOP), bufp);
}

VL_ATTR_COLD void VtopSystolicArray___024root__trace_const_0_sub_0(VtopSystolicArray___024root* vlSelf, VerilatedVcd::Buffer* bufp) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root__trace_const_0_sub_0\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    uint32_t* const oldp VL_ATTR_UNUSED = bufp->oldp(vlSymsp->__Vm_baseCode);
    // Body
    bufp->fullIData(oldp+168,(4U),32);
    bufp->fullBit(oldp+169,(1U));
    bufp->fullIData(oldp+170,(0xaU),32);
    bufp->fullIData(oldp+171,(0x18U),32);
    bufp->fullIData(oldp+172,(0U),24);
}

VL_ATTR_COLD void VtopSystolicArray___024root__trace_full_0_sub_0(VtopSystolicArray___024root* vlSelf, VerilatedVcd::Buffer* bufp);

VL_ATTR_COLD void VtopSystolicArray___024root__trace_full_0(void* voidSelf, VerilatedVcd::Buffer* bufp) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root__trace_full_0\n"); );
    // Init
    VtopSystolicArray___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<VtopSystolicArray___024root*>(voidSelf);
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    // Body
    VtopSystolicArray___024root__trace_full_0_sub_0((&vlSymsp->TOP), bufp);
}

VL_ATTR_COLD void VtopSystolicArray___024root__trace_full_0_sub_0(VtopSystolicArray___024root* vlSelf, VerilatedVcd::Buffer* bufp) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    VtopSystolicArray___024root__trace_full_0_sub_0\n"); );
    VtopSystolicArray__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    uint32_t* const oldp VL_ATTR_UNUSED = bufp->oldp(vlSymsp->__Vm_baseCode);
    VlWide<7>/*223:0*/ __Vtemp_3;
    VlWide<7>/*223:0*/ __Vtemp_6;
    VlWide<5>/*159:0*/ __Vtemp_9;
    VlWide<5>/*159:0*/ __Vtemp_12;
    VlWide<4>/*127:0*/ __Vtemp_14;
    VlWide<4>/*127:0*/ __Vtemp_16;
    // Body
    bufp->fullCData(oldp+1,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_d)
                              ? (0xfU & ((IData)(1U) 
                                         + (IData)(vlSelfRef.topSystolicArray__DOT__counter_q)))
                              : 0U)),4);
    bufp->fullBit(oldp+2,(vlSelfRef.topSystolicArray__DOT__doProcess_d));
    __Vtemp_3[0U] = (IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__);
    __Vtemp_3[1U] = (((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__) 
                      << 0x18U) | (IData)((vlSelfRef.topSystolicArray__DOT__row_d__BRA__55__03a0__KET__ 
                                           >> 0x20U)));
    __Vtemp_3[2U] = (((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__) 
                      >> 8U) | ((IData)((vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
                                         >> 0x20U)) 
                                << 0x18U));
    __Vtemp_3[3U] = (((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__) 
                      << 0x10U) | ((IData)((vlSelfRef.topSystolicArray__DOT__row_d__BRA__111__03a56__KET__ 
                                            >> 0x20U)) 
                                   >> 8U));
    __Vtemp_3[4U] = (((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__) 
                      >> 0x10U) | ((IData)((vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
                                            >> 0x20U)) 
                                   << 0x10U));
    __Vtemp_3[5U] = (((0xff00U & ((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__) 
                                  << 8U)) | ((IData)(
                                                     (vlSelfRef.topSystolicArray__DOT__row_d__BRA__167__03a112__KET__ 
                                                      >> 0x20U)) 
                                             >> 0x10U)) 
                     | (0xffff0000U & ((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__) 
                                       << 8U)));
    __Vtemp_3[6U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__) 
                       >> 0x18U) | (0xff00U & ((IData)(
                                                       (vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
                                                        >> 0x20U)) 
                                               << 8U))) 
                     | (0xffff0000U & ((IData)((vlSelfRef.topSystolicArray__DOT__row_d__BRA__223__03a168__KET__ 
                                                >> 0x20U)) 
                                       << 8U)));
    bufp->fullWData(oldp+3,(__Vtemp_3),224);
    __Vtemp_6[0U] = (IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__);
    __Vtemp_6[1U] = (((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__) 
                      << 0x18U) | (IData)((vlSelfRef.topSystolicArray__DOT__col_d__BRA__55__03a0__KET__ 
                                           >> 0x20U)));
    __Vtemp_6[2U] = (((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__) 
                      >> 8U) | ((IData)((vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
                                         >> 0x20U)) 
                                << 0x18U));
    __Vtemp_6[3U] = (((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__) 
                      << 0x10U) | ((IData)((vlSelfRef.topSystolicArray__DOT__col_d__BRA__111__03a56__KET__ 
                                            >> 0x20U)) 
                                   >> 8U));
    __Vtemp_6[4U] = (((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__) 
                      >> 0x10U) | ((IData)((vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
                                            >> 0x20U)) 
                                   << 0x10U));
    __Vtemp_6[5U] = (((0xff00U & ((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__) 
                                  << 8U)) | ((IData)(
                                                     (vlSelfRef.topSystolicArray__DOT__col_d__BRA__167__03a112__KET__ 
                                                      >> 0x20U)) 
                                             >> 0x10U)) 
                     | (0xffff0000U & ((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__) 
                                       << 8U)));
    __Vtemp_6[6U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__) 
                       >> 0x18U) | (0xff00U & ((IData)(
                                                       (vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
                                                        >> 0x20U)) 
                                               << 8U))) 
                     | (0xffff0000U & ((IData)((vlSelfRef.topSystolicArray__DOT__col_d__BRA__223__03a168__KET__ 
                                                >> 0x20U)) 
                                       << 8U)));
    bufp->fullWData(oldp+10,(__Vtemp_6),224);
    bufp->fullCData(oldp+17,(vlSelfRef.topSystolicArray__DOT__counter_q),4);
    bufp->fullBit(oldp+18,(vlSelfRef.topSystolicArray__DOT__validResult_q));
    bufp->fullBit(oldp+19,(vlSelfRef.topSystolicArray__DOT__doProcess_q));
    bufp->fullWData(oldp+20,(vlSelfRef.topSystolicArray__DOT__row_q),224);
    bufp->fullWData(oldp+27,(vlSelfRef.topSystolicArray__DOT__col_q),224);
    __Vtemp_9[0U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                       << 0x18U) | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                    << 0x10U)) | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                                   << 8U) 
                                                  | (0xffU 
                                                     & vlSelfRef.topSystolicArray__DOT__row_q[0U])));
    __Vtemp_9[1U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                       << 0x18U) | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                    << 0x10U)) | ((0xff00U 
                                                   & (vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                                                      >> 0x10U)) 
                                                  | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q)));
    __Vtemp_9[2U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                       << 0x18U) | (0xff0000U & vlSelfRef.topSystolicArray__DOT__row_q[3U])) 
                     | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q) 
                         << 8U) | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q)));
    __Vtemp_9[3U] = (IData)((((QData)((IData)(((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q) 
                                                 << 0x18U) 
                                                | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                                   << 0x10U)) 
                                               | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                                   << 8U) 
                                                  | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q))))) 
                              << 0x20U) | (QData)((IData)(
                                                          (((0xff000000U 
                                                             & (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                                                                << 0x10U)) 
                                                            | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q) 
                                                               << 0x10U)) 
                                                           | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                                               << 8U) 
                                                              | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q)))))));
    __Vtemp_9[4U] = (IData)(((((QData)((IData)(((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q) 
                                                  << 0x18U) 
                                                 | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                                    << 0x10U)) 
                                                | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                                    << 8U) 
                                                   | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q))))) 
                               << 0x20U) | (QData)((IData)(
                                                           (((0xff000000U 
                                                              & (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                                                                 << 0x10U)) 
                                                             | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q) 
                                                                << 0x10U)) 
                                                            | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                                                << 8U) 
                                                               | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q)))))) 
                             >> 0x20U));
    bufp->fullWData(oldp+34,(__Vtemp_9),160);
    __Vtemp_12[0U] = (((0xff000000U & (vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                                       << 0x10U)) | 
                       (0xff0000U & vlSelfRef.topSystolicArray__DOT__col_q[3U])) 
                      | ((0xff00U & (vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                                     >> 0x10U)) | (0xffU 
                                                   & vlSelfRef.topSystolicArray__DOT__col_q[0U])));
    __Vtemp_12[1U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                        << 0x18U) | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                     << 0x10U)) | (
                                                   ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                                                    << 8U) 
                                                   | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)));
    __Vtemp_12[2U] = ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                        << 0x18U) | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                     << 0x10U)) | (
                                                   ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                                                    << 8U) 
                                                   | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)));
    __Vtemp_12[3U] = (IData)((((QData)((IData)(((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                                                  << 0x18U) 
                                                 | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                                    << 0x10U)) 
                                                | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                                                    << 8U) 
                                                   | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q))))) 
                               << 0x20U) | (QData)((IData)(
                                                           ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                                                              << 0x18U) 
                                                             | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                                                << 0x10U)) 
                                                            | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                                                                << 8U) 
                                                               | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)))))));
    __Vtemp_12[4U] = (IData)(((((QData)((IData)(((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                                                   << 0x18U) 
                                                  | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                                     << 0x10U)) 
                                                 | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                                                     << 8U) 
                                                    | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q))))) 
                                << 0x20U) | (QData)((IData)(
                                                            ((((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q) 
                                                               << 0x18U) 
                                                              | ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q) 
                                                                 << 0x10U)) 
                                                             | (((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q) 
                                                                 << 8U) 
                                                                | (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)))))) 
                              >> 0x20U));
    bufp->fullWData(oldp+39,(__Vtemp_12),160);
    bufp->fullCData(oldp+44,((0xffU & vlSelfRef.topSystolicArray__DOT__row_q[0U])),8);
    bufp->fullCData(oldp+45,((0xffU & vlSelfRef.topSystolicArray__DOT__col_q[0U])),8);
    bufp->fullCData(oldp+46,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+47,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+48,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+49,(((0xffU & vlSelfRef.topSystolicArray__DOT__row_q[0U]) 
                              * (0xffU & vlSelfRef.topSystolicArray__DOT__col_q[0U]))),32);
    bufp->fullIData(oldp+50,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                               ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
                                  + ((0xffU & vlSelfRef.topSystolicArray__DOT__row_q[0U]) 
                                     * (0xffU & vlSelfRef.topSystolicArray__DOT__col_q[0U])))
                               : 0U)),32);
    bufp->fullCData(oldp+51,((vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                              >> 0x18U)),8);
    bufp->fullCData(oldp+52,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+53,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+54,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+55,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                              * (vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                                 >> 0x18U))),32);
    bufp->fullIData(oldp+56,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                               ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
                                  + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                     * (vlSelfRef.topSystolicArray__DOT__col_q[1U] 
                                        >> 0x18U)))
                               : 0U)),32);
    bufp->fullCData(oldp+57,((0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[3U] 
                                       >> 0x10U))),8);
    bufp->fullCData(oldp+58,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+59,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+60,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+61,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                              * (0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[3U] 
                                          >> 0x10U)))),32);
    bufp->fullIData(oldp+62,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                               ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
                                  + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                     * (0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[3U] 
                                                 >> 0x10U))))
                               : 0U)),32);
    bufp->fullCData(oldp+63,((0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                                       >> 8U))),8);
    bufp->fullCData(oldp+64,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+65,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+66,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+67,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                              * (0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                                          >> 8U)))),32);
    bufp->fullIData(oldp+68,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                               ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
                                  + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                     * (0xffU & (vlSelfRef.topSystolicArray__DOT__col_q[5U] 
                                                 >> 8U))))
                               : 0U)),32);
    bufp->fullCData(oldp+69,((vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                              >> 0x18U)),8);
    bufp->fullCData(oldp+70,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+71,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+72,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+73,(((vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                               >> 0x18U) * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+74,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                               ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
                                  + ((vlSelfRef.topSystolicArray__DOT__row_q[1U] 
                                      >> 0x18U) * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)))
                               : 0U)),32);
    bufp->fullCData(oldp+75,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+76,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+77,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+78,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                              * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+79,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                               ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
                                  + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                     * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)))
                               : 0U)),32);
    bufp->fullCData(oldp+80,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+81,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+82,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+83,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                              * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+84,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                               ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
                                  + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                     * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)))
                               : 0U)),32);
    bufp->fullCData(oldp+85,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+86,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+87,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+88,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                              * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+89,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                               ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
                                  + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                     * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__0__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)))
                               : 0U)),32);
    bufp->fullCData(oldp+90,((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[3U] 
                                       >> 0x10U))),8);
    bufp->fullCData(oldp+91,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+92,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+93,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+94,(((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[3U] 
                                        >> 0x10U)) 
                              * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+95,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                               ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
                                  + ((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[3U] 
                                               >> 0x10U)) 
                                     * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)))
                               : 0U)),32);
    bufp->fullCData(oldp+96,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+97,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+98,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+99,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                              * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+100,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
                                   + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                      * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)))
                                : 0U)),32);
    bufp->fullCData(oldp+101,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+102,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+103,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+104,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                               * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+105,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
                                   + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                      * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)))
                                : 0U)),32);
    bufp->fullCData(oldp+106,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+107,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+108,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+109,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                               * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+110,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
                                   + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                      * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__1__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)))
                                : 0U)),32);
    bufp->fullCData(oldp+111,((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                                        >> 8U))),8);
    bufp->fullCData(oldp+112,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+113,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+114,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+115,(((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                                         >> 8U)) * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+116,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__mac_q 
                                   + ((0xffU & (vlSelfRef.topSystolicArray__DOT__row_q[5U] 
                                                >> 8U)) 
                                      * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__b_q)))
                                : 0U)),32);
    bufp->fullCData(oldp+117,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+118,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+119,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+120,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                               * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+121,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__mac_q 
                                   + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__0__KET____DOT__u_pe__DOT__a_q) 
                                      * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__b_q)))
                                : 0U)),32);
    bufp->fullCData(oldp+122,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+123,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+124,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+125,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                               * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+126,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__mac_q 
                                   + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__1__KET____DOT__u_pe__DOT__a_q) 
                                      * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__b_q)))
                                : 0U)),32);
    bufp->fullCData(oldp+127,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__a_q),8);
    bufp->fullCData(oldp+128,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q),8);
    bufp->fullIData(oldp+129,(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q),32);
    bufp->fullIData(oldp+130,(((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                               * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q))),32);
    bufp->fullIData(oldp+131,(((IData)(vlSelfRef.topSystolicArray__DOT__doProcess_q)
                                ? (vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__mac_q 
                                   + ((IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__3__KET____DOT__PerCol__BRA__2__KET____DOT__u_pe__DOT__a_q) 
                                      * (IData)(vlSelfRef.topSystolicArray__DOT__u_systolicArray__DOT__PerRow__BRA__2__KET____DOT__PerCol__BRA__3__KET____DOT__u_pe__DOT__b_q)))
                                : 0U)),32);
    bufp->fullBit(oldp+132,(vlSelfRef.i_clk));
    bufp->fullBit(oldp+133,(vlSelfRef.i_arst));
    bufp->fullWData(oldp+134,(vlSelfRef.i_a),128);
    bufp->fullWData(oldp+138,(vlSelfRef.i_b),128);
    bufp->fullBit(oldp+142,(vlSelfRef.i_validInput));
    bufp->fullWData(oldp+143,(vlSelfRef.o_c),512);
    bufp->fullBit(oldp+159,(vlSelfRef.o_validResult));
    __Vtemp_14[0U] = vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_33;
    __Vtemp_14[1U] = (((vlSelfRef.i_a[1U] << 0x18U) 
                       | (0xff0000U & (vlSelfRef.i_a[1U] 
                                       << 8U))) | (
                                                   (0xff00U 
                                                    & (vlSelfRef.i_a[1U] 
                                                       >> 8U)) 
                                                   | (vlSelfRef.i_a[1U] 
                                                      >> 0x18U)));
    __Vtemp_14[2U] = (IData)((((QData)((IData)((((vlSelfRef.i_a[3U] 
                                                  << 0x18U) 
                                                 | (0xff0000U 
                                                    & (vlSelfRef.i_a[3U] 
                                                       << 8U))) 
                                                | ((0xff00U 
                                                    & (vlSelfRef.i_a[3U] 
                                                       >> 8U)) 
                                                   | (vlSelfRef.i_a[3U] 
                                                      >> 0x18U))))) 
                               << 0x20U) | (QData)((IData)(
                                                           (((vlSelfRef.i_a[2U] 
                                                              << 0x18U) 
                                                             | (0xff0000U 
                                                                & (vlSelfRef.i_a[2U] 
                                                                   << 8U))) 
                                                            | ((0xff00U 
                                                                & (vlSelfRef.i_a[2U] 
                                                                   >> 8U)) 
                                                               | (vlSelfRef.i_a[2U] 
                                                                  >> 0x18U)))))));
    __Vtemp_14[3U] = (IData)(((((QData)((IData)((((
                                                   vlSelfRef.i_a[3U] 
                                                   << 0x18U) 
                                                  | (0xff0000U 
                                                     & (vlSelfRef.i_a[3U] 
                                                        << 8U))) 
                                                 | ((0xff00U 
                                                     & (vlSelfRef.i_a[3U] 
                                                        >> 8U)) 
                                                    | (vlSelfRef.i_a[3U] 
                                                       >> 0x18U))))) 
                                << 0x20U) | (QData)((IData)(
                                                            (((vlSelfRef.i_a[2U] 
                                                               << 0x18U) 
                                                              | (0xff0000U 
                                                                 & (vlSelfRef.i_a[2U] 
                                                                    << 8U))) 
                                                             | ((0xff00U 
                                                                 & (vlSelfRef.i_a[2U] 
                                                                    >> 8U)) 
                                                                | (vlSelfRef.i_a[2U] 
                                                                   >> 0x18U)))))) 
                              >> 0x20U));
    bufp->fullWData(oldp+160,(__Vtemp_14),128);
    __Vtemp_16[0U] = vlSelfRef.topSystolicArray__DOT____VdfgRegularize_h948e65d8_0_34;
    __Vtemp_16[1U] = (((0xff000000U & (vlSelfRef.i_b[0U] 
                                       << 0x10U)) | 
                       (0xff0000U & (vlSelfRef.i_b[1U] 
                                     << 8U))) | ((0xff00U 
                                                  & vlSelfRef.i_b[2U]) 
                                                 | (0xffU 
                                                    & (vlSelfRef.i_b[3U] 
                                                       >> 8U))));
    __Vtemp_16[2U] = (IData)((((QData)((IData)((((0xff000000U 
                                                  & vlSelfRef.i_b[0U]) 
                                                 | (0xff0000U 
                                                    & (vlSelfRef.i_b[1U] 
                                                       >> 8U))) 
                                                | ((0xff00U 
                                                    & (vlSelfRef.i_b[2U] 
                                                       >> 0x10U)) 
                                                   | (vlSelfRef.i_b[3U] 
                                                      >> 0x18U))))) 
                               << 0x20U) | (QData)((IData)(
                                                           (((0xff000000U 
                                                              & (vlSelfRef.i_b[0U] 
                                                                 << 8U)) 
                                                             | (0xff0000U 
                                                                & vlSelfRef.i_b[1U])) 
                                                            | ((0xff00U 
                                                                & (vlSelfRef.i_b[2U] 
                                                                   >> 8U)) 
                                                               | (0xffU 
                                                                  & (vlSelfRef.i_b[3U] 
                                                                     >> 0x10U))))))));
    __Vtemp_16[3U] = (IData)(((((QData)((IData)((((0xff000000U 
                                                   & vlSelfRef.i_b[0U]) 
                                                  | (0xff0000U 
                                                     & (vlSelfRef.i_b[1U] 
                                                        >> 8U))) 
                                                 | ((0xff00U 
                                                     & (vlSelfRef.i_b[2U] 
                                                        >> 0x10U)) 
                                                    | (vlSelfRef.i_b[3U] 
                                                       >> 0x18U))))) 
                                << 0x20U) | (QData)((IData)(
                                                            (((0xff000000U 
                                                               & (vlSelfRef.i_b[0U] 
                                                                  << 8U)) 
                                                              | (0xff0000U 
                                                                 & vlSelfRef.i_b[1U])) 
                                                             | ((0xff00U 
                                                                 & (vlSelfRef.i_b[2U] 
                                                                    >> 8U)) 
                                                                | (0xffU 
                                                                   & (vlSelfRef.i_b[3U] 
                                                                      >> 0x10U))))))) 
                              >> 0x20U));
    bufp->fullWData(oldp+164,(__Vtemp_16),128);
}
