// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See VtopSystolicArray.h for the primary calling header

#include "VtopSystolicArray__pch.h"
#include "VtopSystolicArray__Syms.h"
#include "VtopSystolicArray___024root.h"

void VtopSystolicArray___024root___ctor_var_reset(VtopSystolicArray___024root* vlSelf);

VtopSystolicArray___024root::VtopSystolicArray___024root(VtopSystolicArray__Syms* symsp, const char* v__name)
    : VerilatedModule{v__name}
    , vlSymsp{symsp}
 {
    // Reset structure values
    VtopSystolicArray___024root___ctor_var_reset(this);
}

void VtopSystolicArray___024root::__Vconfigure(bool first) {
    (void)first;  // Prevent unused variable warning
}

VtopSystolicArray___024root::~VtopSystolicArray___024root() {
}
