`default_nettype none
`timescale 1ns / 1ps

module tb_systolic_array_wrapper;

  // Parameters
  parameter int unsigned N = 4;
  parameter real CLK_PERIOD = 10.0; // 100MHz

  // DUT signals
  logic i_clk;
  logic i_arst;
  logic o_led_status;
  logic o_led_valid;

  // Clock generation
  initial begin
    i_clk = 1'b0;
    forever #(CLK_PERIOD/2) i_clk = ~i_clk;
  end

  // Reset generation
  initial begin
    i_arst = 1'b1;
    repeat(10) @(posedge i_clk);
    i_arst = 1'b0;
    $display("Reset released at time %t", $time);
  end

  // DUT instantiation
  systolic_array_wrapper #(
    .N(N)
  ) dut (
    .i_clk        (i_clk),
    .i_arst       (i_arst),
    .o_led_status (o_led_status),
    .o_led_valid  (o_led_valid)
  );

  // Monitor signals
  integer valid_count = 0;
  
  always @(posedge i_clk) begin
    if (o_led_valid) begin
      valid_count++;
      $display("Valid result %0d detected at time %t", valid_count, $time);
      
      // Access internal signals for verification (in simulation only)
      $display("Checksum: 0x%08x", dut.checksum);
      $display("Processing cycles completed");
    end
  end

  // Status monitoring
  always @(posedge i_clk) begin
    if (o_led_status && !$past(o_led_status)) begin
      $display("Processing started at time %t", $time);
    end else if (!o_led_status && $past(o_led_status)) begin
      $display("Processing completed at time %t", $time);
    end
  end

  // Test sequence
  initial begin
    $display("Starting systolic array wrapper testbench");
    $display("Matrix size: %0dx%0d", N, N);
    $display("Clock period: %0.1f ns", CLK_PERIOD);
    
    // Wait for reset deassertion
    wait(!i_arst);
    
    // Wait for first processing cycle
    wait(o_led_status);
    $display("First processing cycle started");
    
    // Wait for first valid result
    wait(o_led_valid);
    $display("First result ready");
    
    // Wait for a few more cycles to see periodic operation
    repeat(5) begin
      wait(o_led_valid);
      @(posedge i_clk);
      wait(!o_led_valid);
      $display("Completed processing cycle %0d", valid_count);
    end
    
    $display("Test completed successfully - %0d matrix multiplications performed", valid_count);
    $display("Testbench finished at time %t", $time);
    $finish;
  end

  // Timeout watchdog
  initial begin
    #50000000; // 50ms timeout
    $error("Testbench timeout - simulation too long");
    $finish;
  end

  // Optional: Dump waveforms
  initial begin
    $dumpfile("tb_systolic_array_wrapper.vcd");
    $dumpvars(0, tb_systolic_array_wrapper);
  end

endmodule

`resetall 