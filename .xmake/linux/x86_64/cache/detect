{
    find_program = {
        gcc = "/usr/bin/gcc",
        nim = false,
        ["/usr/bin/gcc"] = "/usr/bin/gcc",
        verilator = "/usr/local/bin/verilator",
        ["/usr/bin/g++"] = "/usr/bin/g++"
    },
    find_programver = {
        ["/usr/bin/gcc"] = "13.3.0-6ubuntu2",
        ["/usr/bin/g++"] = "13.3.0-6ubuntu2"
    },
    ["lib.detect.has_flags"] = {
        ["linux_x86_64_/usr/bin/gcc_13.3.0-6ubuntu2_cxx__-m64_-fPIC"] = true,
        ["linux_x86_64_/usr/bin/gcc_13.3.0-6ubuntu2_cxx_cxflags_-m64_-fvisibility-inlines-hidden"] = true,
        ["linux_x86_64_/usr/bin/gcc_13.3.0-6ubuntu2_cxx_cxflags_-m64_-MMD -MF"] = true,
        ["linux_x86_64_/usr/bin/gcc_13.3.0-6ubuntu2_cxx__-m64_-O0"] = true,
        ["linux_x86_64_/usr/bin/g++_13.3.0-6ubuntu2_ld__-m64 -m64_-fPIC"] = true,
        ["linux_x86_64_/usr/bin/gcc_13.3.0-6ubuntu2_cxx_cxflags_-m64_-fdiagnostics-color=always"] = true,
        ["linux_x86_64_/usr/bin/gcc_13.3.0-6ubuntu2_cxx_cxflags_-m64_-std=c++20"] = true,
        ["linux_x86_64_/usr/bin/gcc_13.3.0-6ubuntu2_cxx_cxflags_-m64_-Wno-gnu-line-marker -Werror"] = true
    },
    find_program_gcc_arch_x86_64_plat_linux_checktoolar = {
        ar = "/usr/bin/ar"
    },
    find_program_gcc_arch_x86_64_plat_linux_checktoolld = {
        ["g++"] = "/usr/bin/g++"
    },
    find_program_gcc_arch_x86_64_plat_linux_checktoolcxx = {
        gcc = "/usr/bin/gcc"
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["/usr/bin/g++_13.3.0-6ubuntu2"] = {
            ["-a"] = true,
            ["--no-export-dynamic"] = true,
            ["--enable-long-section-names"] = true,
            ["-I"] = true,
            ["--no-ld-generated-unwind-info"] = true,
            ["-Y"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--check-sections"] = true,
            ["--remap-inputs"] = true,
            ["--enable-auto-import"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["--print-gc-sections"] = true,
            ["-fini"] = true,
            ["--version-script"] = true,
            ["--as-needed"] = true,
            ["--disable-reloc-section"] = true,
            ["--major-subsystem-version"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["--export-all-symbols"] = true,
            ["--enable-auto-image-base"] = true,
            ["--default-symver"] = true,
            ["--traditional-format"] = true,
            ["-b"] = true,
            ["--disable-auto-import"] = true,
            ["-u"] = true,
            ["-y"] = true,
            ["-F"] = true,
            ["-plugin"] = true,
            ["--filter"] = true,
            ["--undefined"] = true,
            ["-V"] = true,
            ["--no-print-gc-sections"] = true,
            ["-Qy"] = true,
            ["--unique"] = true,
            ["--no-demangle"] = true,
            ["--trace-symbol"] = true,
            ["--kill-at"] = true,
            ["--no-print-map-locals"] = true,
            ["-EB"] = true,
            ["--no-check-sections"] = true,
            ["-G"] = true,
            ["--no-keep-memory"] = true,
            ["-init"] = true,
            ["--task-link"] = true,
            ["--disable-auto-image-base"] = true,
            ["--map-whole-files"] = true,
            ["--just-symbols"] = true,
            ["--no-gc-sections"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["--start-group"] = true,
            ["--print-map-discarded"] = true,
            ["--print-map-locals"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["--no-map-whole-files"] = true,
            ["--print-output-format"] = true,
            ["--mri-script"] = true,
            ["--exclude-symbols"] = true,
            ["-debug"] = true,
            ["--force-group-allocation"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--relocatable"] = true,
            ["--section-alignment"] = true,
            ["--help"] = true,
            ["--export-dynamic-symbol"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["--strip-debug"] = true,
            ["--minor-os-version"] = true,
            ["-O"] = true,
            ["--demangle"] = true,
            ["--force-exe-suffix"] = true,
            ["--relax"] = true,
            ["--disable-new-dtags"] = true,
            ["--exclude-libs"] = true,
            ["-Ttext-segment"] = true,
            ["-Trodata-segment"] = true,
            ["--gc-keep-exported"] = true,
            ["--warn-multiple-gp"] = true,
            ["--end-group"] = true,
            ["--target-help"] = true,
            ["--no-eh-frame-hdr"] = true,
            ["--nmagic"] = true,
            ["--discard-none"] = true,
            ["--auxiliary"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["--no-omagic"] = true,
            ["--disable-large-address-aware"] = true,
            ["-Ur"] = true,
            ["--pic-executable"] = true,
            ["--no-warn-execstack"] = true,
            ["--no-define-common"] = true,
            ["--warn-once"] = true,
            ["-o"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--no-undefined"] = true,
            ["--support-old-code"] = true,
            ["--require-defined"] = true,
            ["-dT"] = true,
            ["--reduce-memory-overheads"] = true,
            ["-plugin-opt"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["--warn-execstack-objects"] = true,
            ["--omagic"] = true,
            ["--version"] = true,
            ["--image-base"] = true,
            ["--file-alignment"] = true,
            ["--strip-all"] = true,
            ["--library-path"] = true,
            ["--architecture"] = true,
            ["-Map"] = true,
            ["-Bshareable"] = true,
            ["--pop-state"] = true,
            ["--no-fatal-warnings"] = true,
            ["-T"] = true,
            ["-L"] = true,
            ["-flto"] = true,
            ["--compat-implib"] = true,
            ["--no-ctf-variables"] = true,
            ["-qmagic"] = true,
            ["--eh-frame-hdr"] = true,
            ["--cref"] = true,
            ["--output"] = true,
            ["--remap-inputs-file"] = true,
            ["--no-strip-discarded"] = true,
            ["--major-os-version"] = true,
            ["-e"] = true,
            ["--warn-alternate-em"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--retain-symbols-file"] = true,
            ["--ctf-variables"] = true,
            ["--error-execstack"] = true,
            ["-f"] = true,
            ["--trace"] = true,
            ["-dp"] = true,
            ["--no-as-needed"] = true,
            ["--default-imported-symver"] = true,
            ["--emit-relocs"] = true,
            ["--discard-locals"] = true,
            ["--warn-duplicate-exports"] = true,
            ["--defsym"] = true,
            ["--ld-generated-unwind-info"] = true,
            ["--exclude-modules-for-implib"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["--out-implib"] = true,
            ["-l"] = true,
            ["--subsystem"] = true,
            ["--disable-linker-version"] = true,
            ["--add-stdcall-alias"] = true,
            ["-nostdlib"] = true,
            ["--warn-section-align"] = true,
            ["-Bgroup"] = true,
            ["--minor-subsystem-version"] = true,
            ["--allow-multiple-definition"] = true,
            ["-Tbss"] = true,
            ["--whole-archive"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--print-memory-usage"] = true,
            ["--error-handling-script"] = true,
            ["-R"] = true,
            ["-Bno-symbolic"] = true,
            ["--no-print-map-discarded"] = true,
            ["-assert"] = true,
            ["--warn-textrel"] = true,
            ["--format"] = true,
            ["--default-script"] = true,
            ["--oformat"] = true,
            ["--gpsize"] = true,
            ["--warn-rwx-segments"] = true,
            ["-static"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["--error-unresolved-symbols"] = true,
            ["--dynamic-list-data"] = true,
            ["-A"] = true,
            ["-Tdata"] = true,
            ["--no-relax"] = true,
            ["--stats"] = true,
            ["--heap"] = true,
            ["-rpath"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["--enable-linker-version"] = true,
            ["-rpath-link"] = true,
            ["--sort-common"] = true,
            ["-no-pie"] = true,
            ["-c"] = true,
            ["--dll"] = true,
            ["-Bsymbolic"] = true,
            ["--large-address-aware"] = true,
            ["--print-map"] = true,
            ["--sort-section"] = true,
            ["--no-dynamic-linker"] = true,
            ["-Ttext"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["--split-by-file"] = true,
            ["-Tldata-segment"] = true,
            ["--split-by-reloc"] = true,
            ["--no-undefined-version"] = true,
            ["--section-start"] = true,
            ["-h"] = true,
            ["--enable-reloc-section"] = true,
            ["--orphan-handling"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["--strip-discarded"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["--enable-extra-pep-debug"] = true,
            ["--dynamic-list"] = true,
            ["--warn-execstack"] = true,
            ["--spare-dynamic-tags"] = true,
            ["--wrap"] = true,
            ["--version-exports-section"] = true,
            ["--no-error-execstack"] = true,
            ["--discard-all"] = true,
            ["-EL"] = true,
            ["--enable-new-dtags"] = true,
            ["--print-sysroot"] = true,
            ["--no-warnings"] = true,
            ["--fatal-warnings"] = true,
            ["-m"] = true,
            ["--export-dynamic"] = true,
            ["--disable-long-section-names"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["--library"] = true,
            ["--error-rwx-segments"] = true,
            ["--script"] = true,
            ["--undefined-version"] = true,
            ["--major-image-version"] = true,
            ["--no-whole-archive"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["-P"] = true,
            ["--no-warn-mismatch"] = true,
            ["--dynamic-linker"] = true,
            ["--stack"] = true,
            ["--output-def"] = true,
            ["-g"] = true,
            ["--push-state"] = true,
            ["--entry"] = true,
            ["-z"] = true,
            ["--warn-common"] = true,
            ["-soname"] = true,
            ["--exclude-all-symbols"] = true,
            ["--minor-image-version"] = true,
            ["--dependency-file"] = true,
            ["--no-error-rwx-segments"] = true,
            ["-Bsymbolic-functions"] = true,
            ["--enable-extra-pe-debug"] = true,
            ["--verbose"] = true,
            ["--gc-sections"] = true
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["/usr/bin/gcc_13.3.0-6ubuntu2"] = {
            ["-print-multi-directory"] = true,
            ["-Xlinker"] = true,
            ["-dumpversion"] = true,
            ["-print-multi-os-directory"] = true,
            ["-pipe"] = true,
            ["-dumpspecs"] = true,
            ["-o"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["-save-temps"] = true,
            ["-Xpreprocessor"] = true,
            ["--version"] = true,
            ["-pie"] = true,
            ["-B"] = true,
            ["-c"] = true,
            ["-x"] = true,
            ["-E"] = true,
            ["-time"] = true,
            ["-v"] = true,
            ["-no-canonical-prefixes"] = true,
            ["--param"] = true,
            ["-dumpmachine"] = true,
            ["-print-multi-lib"] = true,
            ["-S"] = true,
            ["-Xassembler"] = true,
            ["-print-libgcc-file-name"] = true,
            ["--target-help"] = true,
            ["-shared"] = true,
            ["-pass-exit-codes"] = true,
            ["-print-sysroot"] = true,
            ["-print-search-dirs"] = true,
            ["-print-multiarch"] = true,
            ["--help"] = true
        }
    }
}