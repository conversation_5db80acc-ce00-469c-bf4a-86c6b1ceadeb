{
    cmdlines = {
        "xmake build -v VtopSystolicArray",
        "xmake lua /home/<USER>/.local/share/xmake/modules/private/utils/statistics.lua --verbose",
        "xmake lua /home/<USER>/.local/share/xmake/actions/build/cleaner.lua --verbose",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode ",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/targets.lua",
        "xmake build -v VtopSystolicArrayTB",
        "xmake run VtopSystolicArrayTB",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/update_intellisense.lua .vscode ",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/targets.lua",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/targets.lua",
        "xmake f -p linux -a x86_64 -m debug",
        "xmake run VtopSystolicArrayTB",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/targetpath.lua VtopSystolicArrayTB",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/target_rundir.lua VtopSystolicArrayTB",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/target_runenvs.lua VtopSystolicArrayTB",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake l /home/<USER>/.cursor-server/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake l /home/<USER>/.vscode-server/extensions/tboox.xmake-vscode-2.4.0/assets/explorer.lua",
        "xmake l /home/<USER>/.vscode-server/extensions/tboox.xmake-vscode-2.4.0/assets/config.lua"
    }
}