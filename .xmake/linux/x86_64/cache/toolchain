{
    tool_target_VtopSystolicArray_linux_x86_64_cxx = {
        toolname = "gcc",
        program = "/usr/bin/gcc",
        toolchain_info = {
            cachekey = "gcc_arch_x86_64_plat_linux",
            plat = "linux",
            name = "gcc",
            arch = "x86_64"
        }
    },
    cuda_arch_x86_64_plat_linux = {
        __checked = true,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    fasm_arch_x86_64_plat_linux = {
        __checked = true,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    swift_arch_x86_64_plat_linux = {
        __checked = true,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    gfortran_arch_x86_64_plat_linux = {
        __checked = true,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    tool_target_VtopSystolicArrayTB_linux_x86_64_cxx = {
        toolname = "gcc",
        program = "/usr/bin/gcc",
        toolchain_info = {
            cachekey = "gcc_arch_x86_64_plat_linux",
            plat = "linux",
            name = "gcc",
            arch = "x86_64"
        }
    },
    go_arch_x86_64_plat_linux = {
        __checked = true,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    yasm_arch_x86_64_plat_linux = {
        __checked = true,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    fpc_arch_x86_64_plat_linux = {
        __checked = true,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    rust_arch_x86_64_plat_linux = {
        __checked = true,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    tool_target_VtopSystolicArray_linux_x86_64_ar = {
        toolname = "ar",
        program = "/usr/bin/ar",
        toolchain_info = {
            cachekey = "gcc_arch_x86_64_plat_linux",
            plat = "linux",
            name = "gcc",
            arch = "x86_64"
        }
    },
    cross_arch_x86_64_plat_linux = {
        __checked = false,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    nim_arch_x86_64_plat_linux = {
        __checked = false,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    nasm_arch_x86_64_plat_linux = {
        __checked = true,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    envs_arch_x86_64_plat_linux = {
        __checked = true,
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    gcc_arch_x86_64_plat_linux = {
        __checked = {
            program = "/usr/bin/gcc",
            name = "gcc"
        },
        __global = true,
        arch = "x86_64",
        plat = "linux"
    },
    tool_target_VtopSystolicArrayTB_linux_x86_64_ld = {
        toolname = "gxx",
        program = "/usr/bin/g++",
        toolchain_info = {
            cachekey = "gcc_arch_x86_64_plat_linux",
            plat = "linux",
            name = "gcc",
            arch = "x86_64"
        }
    },
    verilator_arch_x86_64_packages_verilator_plat_linux = {
        packages = "verilator",
        plat = "linux",
        verilator = "/usr/local/bin/verilator",
        arch = "x86_64",
        __checked = true
    }
}