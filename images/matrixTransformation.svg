<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1151px" height="461px" viewBox="-0.5 -0.5 1151 461" content="&lt;mxfile&gt;&lt;diagram id=&quot;CFXc11E8_x7gcc9-f0ev&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="0" y="0" width="1150" height="460" fill="#bfccde" stroke="#6c8ebf" pointer-events="all"/><rect x="30" y="40" width="150" height="150" fill="#ffffff" stroke="none" pointer-events="none"/><path d="M 30 40 L 180 40 L 180 190 L 30 190 L 30 40" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><path d="M 30 78 L 68 78 L 105 78 L 143 78 L 180 78" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 30 115 L 68 115 L 105 115 L 143 115 L 180 115" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 30 153 L 68 153 L 105 153 L 143 153 L 180 153" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 68 40 L 68 78 L 68 115 L 68 153 L 68 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 105 40 L 105 78 L 105 115 L 105 153 L 105 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 143 40 L 143 78 L 143 115 L 143 153 L 143 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 30 40 M 68 40 M 68 78 M 30 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 59px; margin-left: 31px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000" style="font-size: 20px;">00</font></div></div></div></foreignObject><text x="49" y="63" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">00</text></switch></g><path d="M 68 40 M 105 40 M 105 78 M 68 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 69px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">01</div></div></div></foreignObject><text x="87" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">01</text></switch></g><path d="M 105 40 M 143 40 M 143 78 M 105 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 59px; margin-left: 106px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">02</div></div></div></foreignObject><text x="124" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">02</text></switch></g><path d="M 143 40 M 180 40 M 180 78 M 143 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 144px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">03</div></div></div></foreignObject><text x="162" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">03</text></switch></g><path d="M 30 78 M 68 78 M 68 115 M 30 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 97px; margin-left: 31px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">10</div></div></div></foreignObject><text x="49" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">10</text></switch></g><path d="M 68 78 M 105 78 M 105 115 M 68 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 69px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">11</div></div></div></foreignObject><text x="87" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">11</text></switch></g><path d="M 105 78 M 143 78 M 143 115 M 105 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 97px; margin-left: 106px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">12</div></div></div></foreignObject><text x="124" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">12</text></switch></g><path d="M 143 78 M 180 78 M 180 115 M 143 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 144px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">13</div></div></div></foreignObject><text x="162" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">13</text></switch></g><path d="M 30 115 M 68 115 M 68 153 M 30 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 134px; margin-left: 31px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">20</div></div></div></foreignObject><text x="49" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">20</text></switch></g><path d="M 68 115 M 105 115 M 105 153 M 68 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 69px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">21</div></div></div></foreignObject><text x="87" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">21</text></switch></g><path d="M 105 115 M 143 115 M 143 153 M 105 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 134px; margin-left: 106px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">22</div></div></div></foreignObject><text x="124" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">22</text></switch></g><path d="M 143 115 M 180 115 M 180 153 M 143 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 144px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">23</div></div></div></foreignObject><text x="162" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">23</text></switch></g><path d="M 30 153 M 68 153 M 68 190 M 30 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 172px; margin-left: 31px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">30</div></div></div></foreignObject><text x="49" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">30</text></switch></g><path d="M 68 153 M 105 153 M 105 190 M 68 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 69px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">31</div></div></div></foreignObject><text x="87" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">31</text></switch></g><path d="M 105 153 M 143 153 M 143 190 M 105 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 172px; margin-left: 106px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">32</div></div></div></foreignObject><text x="124" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">32</text></switch></g><path d="M 143 153 M 180 153 M 180 190 M 143 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 144px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">33</div></div></div></foreignObject><text x="162" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">33</text></switch></g><rect x="30" y="280" width="150" height="150" fill="#ffffff" stroke="none" pointer-events="none"/><path d="M 30 280 L 180 280 L 180 430 L 30 430 L 30 280" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><path d="M 30 318 L 68 318 L 105 318 L 143 318 L 180 318" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 30 355 L 68 355 L 105 355 L 143 355 L 180 355" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 30 393 L 68 393 L 105 393 L 143 393 L 180 393" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 68 280 L 68 318 L 68 355 L 68 393 L 68 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 105 280 L 105 318 L 105 355 L 105 393 L 105 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 143 280 L 143 318 L 143 355 L 143 393 L 143 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 30 280 M 68 280 M 68 318 M 30 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 299px; margin-left: 31px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000" style="font-size: 20px;">00</font></div></div></div></foreignObject><text x="49" y="303" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">00</text></switch></g><path d="M 68 280 M 105 280 M 105 318 M 68 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 69px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">01</div></div></div></foreignObject><text x="87" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">01</text></switch></g><path d="M 105 280 M 143 280 M 143 318 M 105 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 299px; margin-left: 106px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">02</div></div></div></foreignObject><text x="124" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">02</text></switch></g><path d="M 143 280 M 180 280 M 180 318 M 143 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 144px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">03</div></div></div></foreignObject><text x="162" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">03</text></switch></g><path d="M 30 318 M 68 318 M 68 355 M 30 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 337px; margin-left: 31px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">10</div></div></div></foreignObject><text x="49" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">10</text></switch></g><path d="M 68 318 M 105 318 M 105 355 M 68 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 69px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">11</div></div></div></foreignObject><text x="87" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">11</text></switch></g><path d="M 105 318 M 143 318 M 143 355 M 105 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 337px; margin-left: 106px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">12</div></div></div></foreignObject><text x="124" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">12</text></switch></g><path d="M 143 318 M 180 318 M 180 355 M 143 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 144px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">13</div></div></div></foreignObject><text x="162" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">13</text></switch></g><path d="M 30 355 M 68 355 M 68 393 M 30 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 374px; margin-left: 31px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">20</div></div></div></foreignObject><text x="49" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">20</text></switch></g><path d="M 68 355 M 105 355 M 105 393 M 68 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 69px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">21</div></div></div></foreignObject><text x="87" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">21</text></switch></g><path d="M 105 355 M 143 355 M 143 393 M 105 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 374px; margin-left: 106px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">22</div></div></div></foreignObject><text x="124" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">22</text></switch></g><path d="M 143 355 M 180 355 M 180 393 M 143 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 144px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">23</div></div></div></foreignObject><text x="162" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">23</text></switch></g><path d="M 30 393 M 68 393 M 68 430 M 30 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 412px; margin-left: 31px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">30</div></div></div></foreignObject><text x="49" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">30</text></switch></g><path d="M 68 393 M 105 393 M 105 430 M 68 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 69px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">31</div></div></div></foreignObject><text x="87" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">31</text></switch></g><path d="M 105 393 M 143 393 M 143 430 M 105 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 412px; margin-left: 106px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">32</div></div></div></foreignObject><text x="124" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">32</text></switch></g><path d="M 143 393 M 180 393 M 180 430 M 143 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 144px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">33</div></div></div></foreignObject><text x="162" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">33</text></switch></g><path d="M 190.5 125 L 190.5 115 L 240.5 115 L 240.5 104.5 L 259.5 120 L 240.5 135.5 L 240.5 125 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="none"/><path d="M 190.46 364.41 L 190.54 354.41 L 240.54 354.84 L 240.63 344.34 L 259.5 360 L 240.37 375.34 L 240.46 364.84 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="none"/><rect x="270" y="40" width="150" height="150" fill="#ffffff" stroke="none" pointer-events="none"/><path d="M 270 40 L 420 40 L 420 190 L 270 190 L 270 40" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><path d="M 270 78 L 308 78 L 345 78 L 383 78 L 420 78" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 270 115 L 308 115 L 345 115 L 383 115 L 420 115" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 270 153 L 308 153 L 345 153 L 383 153 L 420 153" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 308 40 L 308 78 L 308 115 L 308 153 L 308 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 345 40 L 345 78 L 345 115 L 345 153 L 345 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 383 40 L 383 78 L 383 115 L 383 153 L 383 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 270 40 M 308 40 M 308 78 M 270 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 59px; margin-left: 271px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000" style="font-size: 20px;">03</font></div></div></div></foreignObject><text x="289" y="63" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">03</text></switch></g><path d="M 308 40 M 345 40 M 345 78 M 308 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 309px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">02</div></div></div></foreignObject><text x="327" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">02</text></switch></g><path d="M 345 40 M 383 40 M 383 78 M 345 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 59px; margin-left: 346px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">01</div></div></div></foreignObject><text x="364" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">01</text></switch></g><path d="M 383 40 M 420 40 M 420 78 M 383 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 384px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="402" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 270 78 M 308 78 M 308 115 M 270 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 97px; margin-left: 271px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">13</div></div></div></foreignObject><text x="289" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">13</text></switch></g><path d="M 308 78 M 345 78 M 345 115 M 308 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 309px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">12</div></div></div></foreignObject><text x="327" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">12</text></switch></g><path d="M 345 78 M 383 78 M 383 115 M 345 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 97px; margin-left: 346px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">11</div></div></div></foreignObject><text x="364" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">11</text></switch></g><path d="M 383 78 M 420 78 M 420 115 M 383 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 384px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">10</div></div></div></foreignObject><text x="402" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">10</text></switch></g><path d="M 270 115 M 308 115 M 308 153 M 270 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 134px; margin-left: 271px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">23</div></div></div></foreignObject><text x="289" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">23</text></switch></g><path d="M 308 115 M 345 115 M 345 153 M 308 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 309px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">22</div></div></div></foreignObject><text x="327" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">22</text></switch></g><path d="M 345 115 M 383 115 M 383 153 M 345 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 134px; margin-left: 346px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">21</div></div></div></foreignObject><text x="364" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">21</text></switch></g><path d="M 383 115 M 420 115 M 420 153 M 383 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 384px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">20</div></div></div></foreignObject><text x="402" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">20</text></switch></g><path d="M 270 153 M 308 153 M 308 190 M 270 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 172px; margin-left: 271px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">33</div></div></div></foreignObject><text x="289" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">33</text></switch></g><path d="M 308 153 M 345 153 M 345 190 M 308 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 309px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">32</div></div></div></foreignObject><text x="327" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">32</text></switch></g><path d="M 345 153 M 383 153 M 383 190 M 345 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 172px; margin-left: 346px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">31</div></div></div></foreignObject><text x="364" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">31</text></switch></g><path d="M 383 153 M 420 153 M 420 190 M 383 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 384px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">30</div></div></div></foreignObject><text x="402" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">30</text></switch></g><rect x="270" y="280" width="150" height="150" fill="#ffffff" stroke="none" pointer-events="none"/><path d="M 270 280 L 420 280 L 420 430 L 270 430 L 270 280" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><path d="M 270 318 L 308 318 L 345 318 L 383 318 L 420 318" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 270 355 L 308 355 L 345 355 L 383 355 L 420 355" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 270 393 L 308 393 L 345 393 L 383 393 L 420 393" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 308 280 L 308 318 L 308 355 L 308 393 L 308 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 345 280 L 345 318 L 345 355 L 345 393 L 345 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 383 280 L 383 318 L 383 355 L 383 393 L 383 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 270 280 M 308 280 M 308 318 M 270 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 299px; margin-left: 271px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000" style="font-size: 20px;">30</font></div></div></div></foreignObject><text x="289" y="303" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">30</text></switch></g><path d="M 308 280 M 345 280 M 345 318 M 308 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 309px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">20</div></div></div></foreignObject><text x="327" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">20</text></switch></g><path d="M 345 280 M 383 280 M 383 318 M 345 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 299px; margin-left: 346px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">10</div></div></div></foreignObject><text x="364" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">10</text></switch></g><path d="M 383 280 M 420 280 M 420 318 M 383 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 384px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="402" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 270 318 M 308 318 M 308 355 M 270 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 337px; margin-left: 271px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">31</div></div></div></foreignObject><text x="289" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">31</text></switch></g><path d="M 308 318 M 345 318 M 345 355 M 308 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 309px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">21</div></div></div></foreignObject><text x="327" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">21</text></switch></g><path d="M 345 318 M 383 318 M 383 355 M 345 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 337px; margin-left: 346px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">11</div></div></div></foreignObject><text x="364" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">11</text></switch></g><path d="M 383 318 M 420 318 M 420 355 M 383 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 384px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">01</div></div></div></foreignObject><text x="402" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">01</text></switch></g><path d="M 270 355 M 308 355 M 308 393 M 270 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 374px; margin-left: 271px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">32</div></div></div></foreignObject><text x="289" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">32</text></switch></g><path d="M 308 355 M 345 355 M 345 393 M 308 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 309px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">22</div></div></div></foreignObject><text x="327" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">22</text></switch></g><path d="M 345 355 M 383 355 M 383 393 M 345 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 374px; margin-left: 346px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">12</div></div></div></foreignObject><text x="364" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">12</text></switch></g><path d="M 383 355 M 420 355 M 420 393 M 383 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 384px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">02</div></div></div></foreignObject><text x="402" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">02</text></switch></g><path d="M 270 393 M 308 393 M 308 430 M 270 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 412px; margin-left: 271px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">33</div></div></div></foreignObject><text x="289" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">33</text></switch></g><path d="M 308 393 M 345 393 M 345 430 M 308 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 309px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">23</div></div></div></foreignObject><text x="327" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">23</text></switch></g><path d="M 345 393 M 383 393 M 383 430 M 345 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 412px; margin-left: 346px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">13</div></div></div></foreignObject><text x="364" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">13</text></switch></g><path d="M 383 393 M 420 393 M 420 430 M 383 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 384px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">03</div></div></div></foreignObject><text x="402" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">03</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 15px; margin-left: 61px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">i_a</div></div></div></foreignObject><text x="90" y="21" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">i_a</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 255px; margin-left: 61px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">i_b</div></div></div></foreignObject><text x="90" y="261" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">i_b</text></switch></g><path d="M 430.5 125 L 430.5 115 L 480.5 115 L 480.5 104.5 L 499.5 120 L 480.5 135.5 L 480.5 125 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="none"/><path d="M 430.46 364.41 L 430.54 354.41 L 480.54 354.84 L 480.63 344.34 L 499.5 360 L 480.37 375.34 L 480.46 364.84 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="none"/><rect x="510" y="40" width="261" height="150" fill="#ffffff" stroke="none" pointer-events="none"/><path d="M 510 40 L 771 40 L 771 190 L 510 190 L 510 40" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><path d="M 510 78 L 548 78 L 585 78 L 623 78 L 660 78 L 697 78 L 734 78 L 771 78" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 510 115 L 548 115 L 585 115 L 623 115 L 660 115 L 697 115 L 734 115 L 771 115" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 510 153 L 548 153 L 585 153 L 623 153 L 660 153 L 697 153 L 734 153 L 771 153" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 548 40 L 548 78 L 548 115 L 548 153 L 548 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 585 40 L 585 78 L 585 115 L 585 153 L 585 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 623 40 L 623 78 L 623 115 L 623 153 L 623 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 40 L 660 78 L 660 115 L 660 153 L 660 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 697 40 L 697 78 L 697 115 L 697 153 L 697 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 734 40 L 734 78 L 734 115 L 734 153 L 734 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 510 40 M 548 40 M 548 78 M 510 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 59px; margin-left: 511px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000" style="font-size: 20px;">03</font></div></div></div></foreignObject><text x="529" y="63" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">03</text></switch></g><path d="M 548 40 M 585 40 M 585 78 M 548 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 549px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">02</div></div></div></foreignObject><text x="567" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">02</text></switch></g><path d="M 585 40 M 623 40 M 623 78 M 585 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 59px; margin-left: 586px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">01</div></div></div></foreignObject><text x="604" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">01</text></switch></g><path d="M 623 40 M 660 40 M 660 78 M 623 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 624px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="642" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 660 40 M 697 40 M 697 78 M 660 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 661px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="679" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 697 40 M 734 40 M 734 78 M 697 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 698px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="716" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 734 40 M 771 40 M 771 78 M 734 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 735px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="753" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 510 78 M 548 78 M 548 115 M 510 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 97px; margin-left: 511px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">13</div></div></div></foreignObject><text x="529" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">13</text></switch></g><path d="M 548 78 M 585 78 M 585 115 M 548 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 549px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">12</div></div></div></foreignObject><text x="567" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">12</text></switch></g><path d="M 585 78 M 623 78 M 623 115 M 585 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 97px; margin-left: 586px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">11</div></div></div></foreignObject><text x="604" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">11</text></switch></g><path d="M 623 78 M 660 78 M 660 115 M 623 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 624px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">10</div></div></div></foreignObject><text x="642" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">10</text></switch></g><path d="M 660 78 M 697 78 M 697 115 M 660 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 661px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="679" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 697 78 M 734 78 M 734 115 M 697 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 698px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="716" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 734 78 M 771 78 M 771 115 M 734 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 735px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="753" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 510 115 M 548 115 M 548 153 M 510 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 134px; margin-left: 511px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">23</div></div></div></foreignObject><text x="529" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">23</text></switch></g><path d="M 548 115 M 585 115 M 585 153 M 548 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 549px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">22</div></div></div></foreignObject><text x="567" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">22</text></switch></g><path d="M 585 115 M 623 115 M 623 153 M 585 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 134px; margin-left: 586px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">21</div></div></div></foreignObject><text x="604" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">21</text></switch></g><path d="M 623 115 M 660 115 M 660 153 M 623 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 624px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">20</div></div></div></foreignObject><text x="642" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">20</text></switch></g><path d="M 660 115 M 697 115 M 697 153 M 660 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 661px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="679" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 697 115 M 734 115 M 734 153 M 697 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 698px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="716" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 734 115 M 771 115 M 771 153 M 734 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 735px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="753" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 510 153 M 548 153 M 548 190 M 510 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 172px; margin-left: 511px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">33</div></div></div></foreignObject><text x="529" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">33</text></switch></g><path d="M 548 153 M 585 153 M 585 190 M 548 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 549px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">32</div></div></div></foreignObject><text x="567" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">32</text></switch></g><path d="M 585 153 M 623 153 M 623 190 M 585 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 172px; margin-left: 586px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">31</div></div></div></foreignObject><text x="604" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">31</text></switch></g><path d="M 623 153 M 660 153 M 660 190 M 623 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 624px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">30</div></div></div></foreignObject><text x="642" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">30</text></switch></g><path d="M 660 153 M 697 153 M 697 190 M 660 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 661px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="679" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 697 153 M 734 153 M 734 190 M 697 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 698px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="716" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 734 153 M 771 153 M 771 190 M 734 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 735px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="753" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><rect x="510" y="280" width="261" height="150" fill="#ffffff" stroke="none" pointer-events="none"/><path d="M 510 280 L 771 280 L 771 430 L 510 430 L 510 280" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><path d="M 510 318 L 548 318 L 585 318 L 623 318 L 660 318 L 697 318 L 734 318 L 771 318" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 510 355 L 548 355 L 585 355 L 623 355 L 660 355 L 697 355 L 734 355 L 771 355" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 510 393 L 548 393 L 585 393 L 623 393 L 660 393 L 697 393 L 734 393 L 771 393" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 548 280 L 548 318 L 548 355 L 548 393 L 548 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 585 280 L 585 318 L 585 355 L 585 393 L 585 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 623 280 L 623 318 L 623 355 L 623 393 L 623 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 660 280 L 660 318 L 660 355 L 660 393 L 660 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 697 280 L 697 318 L 697 355 L 697 393 L 697 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 734 280 L 734 318 L 734 355 L 734 393 L 734 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 510 280 M 548 280 M 548 318 M 510 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 299px; margin-left: 511px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000" style="font-size: 20px;">30</font></div></div></div></foreignObject><text x="529" y="303" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">30</text></switch></g><path d="M 548 280 M 585 280 M 585 318 M 548 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 549px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">20</div></div></div></foreignObject><text x="567" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">20</text></switch></g><path d="M 585 280 M 623 280 M 623 318 M 585 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 299px; margin-left: 586px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">10</div></div></div></foreignObject><text x="604" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">10</text></switch></g><path d="M 623 280 M 660 280 M 660 318 M 623 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 624px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="642" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 660 280 M 697 280 M 697 318 M 660 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 661px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="679" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 697 280 M 734 280 M 734 318 M 697 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 698px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="716" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 734 280 M 771 280 M 771 318 M 734 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 735px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="753" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 510 318 M 548 318 M 548 355 M 510 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 337px; margin-left: 511px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">31</div></div></div></foreignObject><text x="529" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">31</text></switch></g><path d="M 548 318 M 585 318 M 585 355 M 548 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 549px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">21</div></div></div></foreignObject><text x="567" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">21</text></switch></g><path d="M 585 318 M 623 318 M 623 355 M 585 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 337px; margin-left: 586px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">11</div></div></div></foreignObject><text x="604" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">11</text></switch></g><path d="M 623 318 M 660 318 M 660 355 M 623 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 624px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">01</div></div></div></foreignObject><text x="642" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">01</text></switch></g><path d="M 660 318 M 697 318 M 697 355 M 660 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 661px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="679" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 697 318 M 734 318 M 734 355 M 697 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 698px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="716" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 734 318 M 771 318 M 771 355 M 734 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 735px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="753" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 510 355 M 548 355 M 548 393 M 510 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 374px; margin-left: 511px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">32</div></div></div></foreignObject><text x="529" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">32</text></switch></g><path d="M 548 355 M 585 355 M 585 393 M 548 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 549px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">22</div></div></div></foreignObject><text x="567" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">22</text></switch></g><path d="M 585 355 M 623 355 M 623 393 M 585 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 374px; margin-left: 586px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">12</div></div></div></foreignObject><text x="604" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">12</text></switch></g><path d="M 623 355 M 660 355 M 660 393 M 623 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 624px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">02</div></div></div></foreignObject><text x="642" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">02</text></switch></g><path d="M 660 355 M 697 355 M 697 393 M 660 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 661px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="679" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 697 355 M 734 355 M 734 393 M 697 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 698px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="716" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 734 355 M 771 355 M 771 393 M 734 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 735px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="753" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 510 393 M 548 393 M 548 430 M 510 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 412px; margin-left: 511px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">33</div></div></div></foreignObject><text x="529" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">33</text></switch></g><path d="M 548 393 M 585 393 M 585 430 M 548 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 549px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">23</div></div></div></foreignObject><text x="567" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">23</text></switch></g><path d="M 585 393 M 623 393 M 623 430 M 585 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 412px; margin-left: 586px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">13</div></div></div></foreignObject><text x="604" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">13</text></switch></g><path d="M 623 393 M 660 393 M 660 430 M 623 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 624px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">03</div></div></div></foreignObject><text x="642" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">03</text></switch></g><path d="M 660 393 M 697 393 M 697 430 M 660 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 661px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="679" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 697 393 M 734 393 M 734 430 M 697 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 698px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="716" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 734 393 M 771 393 M 771 430 M 734 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 735px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="753" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 779.5 125 L 779.5 115 L 829.5 115 L 829.5 104.5 L 848.5 120 L 829.5 135.5 L 829.5 125 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="none"/><path d="M 779.46 364.41 L 779.54 354.41 L 829.54 354.84 L 829.63 344.34 L 848.5 360 L 829.37 375.34 L 829.46 364.84 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="none"/><rect x="859" y="40" width="261" height="150" fill="#ffffff" stroke="none" pointer-events="none"/><path d="M 859 40 L 1120 40 L 1120 190 L 859 190 L 859 40" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><path d="M 859 78 L 897 78 L 934 78 L 972 78 L 1009 78 L 1046 78 L 1083 78 L 1120 78" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 859 115 L 897 115 L 934 115 L 972 115 L 1009 115 L 1046 115 L 1083 115 L 1120 115" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 859 153 L 897 153 L 934 153 L 972 153 L 1009 153 L 1046 153 L 1083 153 L 1120 153" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 897 40 L 897 78 L 897 115 L 897 153 L 897 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 934 40 L 934 78 L 934 115 L 934 153 L 934 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 972 40 L 972 78 L 972 115 L 972 153 L 972 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1009 40 L 1009 78 L 1009 115 L 1009 153 L 1009 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1046 40 L 1046 78 L 1046 115 L 1046 153 L 1046 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1083 40 L 1083 78 L 1083 115 L 1083 153 L 1083 190" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 859 40 M 897 40 M 897 78 M 859 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 59px; margin-left: 860px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000" style="font-size: 20px;">03</font></div></div></div></foreignObject><text x="878" y="63" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">03</text></switch></g><path d="M 897 40 M 934 40 M 934 78 M 897 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 898px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">02</div></div></div></foreignObject><text x="916" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">02</text></switch></g><path d="M 934 40 M 972 40 M 972 78 M 934 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 59px; margin-left: 935px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">01</div></div></div></foreignObject><text x="953" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">01</text></switch></g><path d="M 972 40 M 1009 40 M 1009 78 M 972 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 973px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="991" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1009 40 M 1046 40 M 1046 78 M 1009 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 1010px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1028" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1046 40 M 1083 40 M 1083 78 M 1046 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 1047px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1065" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1083 40 M 1120 40 M 1120 78 M 1083 78" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 59px; margin-left: 1084px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1102" y="65" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 859 78 M 897 78 M 897 115 M 859 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 97px; margin-left: 860px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="878" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 897 78 M 934 78 M 934 115 M 897 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 898px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">13</div></div></div></foreignObject><text x="916" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">13</text></switch></g><path d="M 934 78 M 972 78 M 972 115 M 934 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 97px; margin-left: 935px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">12</div></div></div></foreignObject><text x="953" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">12</text></switch></g><path d="M 972 78 M 1009 78 M 1009 115 M 972 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 973px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">11</div></div></div></foreignObject><text x="991" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">11</text></switch></g><path d="M 1009 78 M 1046 78 M 1046 115 M 1009 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 1010px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">10</div></div></div></foreignObject><text x="1028" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">10</text></switch></g><path d="M 1046 78 M 1083 78 M 1083 115 M 1046 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 1047px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1065" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1083 78 M 1120 78 M 1120 115 M 1083 115" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 97px; margin-left: 1084px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1102" y="103" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 859 115 M 897 115 M 897 153 M 859 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 134px; margin-left: 860px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="878" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 897 115 M 934 115 M 934 153 M 897 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 898px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="916" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 934 115 M 972 115 M 972 153 M 934 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 134px; margin-left: 935px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">23</div></div></div></foreignObject><text x="953" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">23</text></switch></g><path d="M 972 115 M 1009 115 M 1009 153 M 972 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 973px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">22</div></div></div></foreignObject><text x="991" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">22</text></switch></g><path d="M 1009 115 M 1046 115 M 1046 153 M 1009 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 1010px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">21</div></div></div></foreignObject><text x="1028" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">21</text></switch></g><path d="M 1046 115 M 1083 115 M 1083 153 M 1046 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 1047px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">20</div></div></div></foreignObject><text x="1065" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">20</text></switch></g><path d="M 1083 115 M 1120 115 M 1120 153 M 1083 153" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 134px; margin-left: 1084px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1102" y="140" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 859 153 M 897 153 M 897 190 M 859 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 172px; margin-left: 860px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="878" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 897 153 M 934 153 M 934 190 M 897 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 898px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="916" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 934 153 M 972 153 M 972 190 M 934 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 172px; margin-left: 935px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="953" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 972 153 M 1009 153 M 1009 190 M 972 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 973px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">33</div></div></div></foreignObject><text x="991" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">33</text></switch></g><path d="M 1009 153 M 1046 153 M 1046 190 M 1009 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 1010px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">32</div></div></div></foreignObject><text x="1028" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">32</text></switch></g><path d="M 1046 153 M 1083 153 M 1083 190 M 1046 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 1047px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">31</div></div></div></foreignObject><text x="1065" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">31</text></switch></g><path d="M 1083 153 M 1120 153 M 1120 190 M 1083 190" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 172px; margin-left: 1084px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">30</div></div></div></foreignObject><text x="1102" y="178" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">30</text></switch></g><rect x="859" y="280" width="261" height="150" fill="#ffffff" stroke="none" pointer-events="none"/><path d="M 859 280 L 1120 280 L 1120 430 L 859 430 L 859 280" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><path d="M 859 318 L 897 318 L 934 318 L 972 318 L 1009 318 L 1046 318 L 1083 318 L 1120 318" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 859 355 L 897 355 L 934 355 L 972 355 L 1009 355 L 1046 355 L 1083 355 L 1120 355" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 859 393 L 897 393 L 934 393 L 972 393 L 1009 393 L 1046 393 L 1083 393 L 1120 393" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 897 280 L 897 318 L 897 355 L 897 393 L 897 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 934 280 L 934 318 L 934 355 L 934 393 L 934 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 972 280 L 972 318 L 972 355 L 972 393 L 972 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1009 280 L 1009 318 L 1009 355 L 1009 393 L 1009 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1046 280 L 1046 318 L 1046 355 L 1046 393 L 1046 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1083 280 L 1083 318 L 1083 355 L 1083 393 L 1083 430" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 859 280 M 897 280 M 897 318 M 859 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 299px; margin-left: 860px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000" style="font-size: 20px;">30</font></div></div></div></foreignObject><text x="878" y="303" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">30</text></switch></g><path d="M 897 280 M 934 280 M 934 318 M 897 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 898px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">20</div></div></div></foreignObject><text x="916" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">20</text></switch></g><path d="M 934 280 M 972 280 M 972 318 M 934 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 299px; margin-left: 935px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">10</div></div></div></foreignObject><text x="953" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">10</text></switch></g><path d="M 972 280 M 1009 280 M 1009 318 M 972 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 973px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="991" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1009 280 M 1046 280 M 1046 318 M 1009 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 1010px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1028" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1046 280 M 1083 280 M 1083 318 M 1046 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 1047px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1065" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1083 280 M 1120 280 M 1120 318 M 1083 318" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 299px; margin-left: 1084px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1102" y="305" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 859 318 M 897 318 M 897 355 M 859 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 337px; margin-left: 860px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="878" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 897 318 M 934 318 M 934 355 M 897 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 898px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">31</div></div></div></foreignObject><text x="916" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">31</text></switch></g><path d="M 934 318 M 972 318 M 972 355 M 934 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 337px; margin-left: 935px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">21</div></div></div></foreignObject><text x="953" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">21</text></switch></g><path d="M 972 318 M 1009 318 M 1009 355 M 972 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 973px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">11</div></div></div></foreignObject><text x="991" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">11</text></switch></g><path d="M 1009 318 M 1046 318 M 1046 355 M 1009 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 1010px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">01</div></div></div></foreignObject><text x="1028" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">01</text></switch></g><path d="M 1046 318 M 1083 318 M 1083 355 M 1046 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 1047px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1065" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1083 318 M 1120 318 M 1120 355 M 1083 355" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 337px; margin-left: 1084px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1102" y="343" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 859 355 M 897 355 M 897 393 M 859 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 374px; margin-left: 860px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="878" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 897 355 M 934 355 M 934 393 M 897 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 898px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="916" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 934 355 M 972 355 M 972 393 M 934 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 374px; margin-left: 935px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">32</div></div></div></foreignObject><text x="953" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">32</text></switch></g><path d="M 972 355 M 1009 355 M 1009 393 M 972 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 973px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">22</div></div></div></foreignObject><text x="991" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">22</text></switch></g><path d="M 1009 355 M 1046 355 M 1046 393 M 1009 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 1010px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">12</div></div></div></foreignObject><text x="1028" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">12</text></switch></g><path d="M 1046 355 M 1083 355 M 1083 393 M 1046 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 1047px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">02</div></div></div></foreignObject><text x="1065" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">02</text></switch></g><path d="M 1083 355 M 1120 355 M 1120 393 M 1083 393" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 374px; margin-left: 1084px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1102" y="380" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 859 393 M 897 393 M 897 430 M 859 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 412px; margin-left: 860px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="878" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 897 393 M 934 393 M 934 430 M 897 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 898px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="916" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 934 393 M 972 393 M 972 430 M 934 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 412px; margin-left: 935px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="953" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 972 393 M 1009 393 M 1009 430 M 972 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 973px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">33</div></div></div></foreignObject><text x="991" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">33</text></switch></g><path d="M 1009 393 M 1046 393 M 1046 430 M 1009 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 1010px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">23</div></div></div></foreignObject><text x="1028" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">23</text></switch></g><path d="M 1046 393 M 1083 393 M 1083 430 M 1046 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 1047px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">13</div></div></div></foreignObject><text x="1065" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">13</text></switch></g><path d="M 1083 393 M 1120 393 M 1120 430 M 1083 430" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 412px; margin-left: 1084px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">03</div></div></div></foreignObject><text x="1102" y="418" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">03</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 15px; margin-left: 961px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">row<br /></div></div></div></foreignObject><text x="990" y="21" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">row
</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 255px; margin-left: 961px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">column</div></div></div></foreignObject><text x="990" y="261" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">column</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>