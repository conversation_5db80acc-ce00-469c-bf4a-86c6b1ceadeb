<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1201px" height="742px" viewBox="-0.5 -0.5 1201 742" content="&lt;mxfile&gt;&lt;diagram id=&quot;f-B_nj9lfbYI7uDCfrp8&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="0" y="0" width="1200" height="740" fill="#ffffff" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="400" y="200" width="490" height="520" rx="24.5" ry="24.5" fill="#bdbdbd" stroke="#36393d" pointer-events="all"/><path d="M 479.98 233.63 L 479.9 200" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 480 238.88 L 476.48 231.89 L 479.98 233.63 L 483.48 231.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 433.63 280.02 L 400 280.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 438.88 280 L 431.89 283.52 L 433.63 280.02 L 431.87 276.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="440" y="240" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 440 260 L 520 260" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 460 320 L 460 260" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 599.98 233.63 L 599.9 200" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 600 238.88 L 596.48 231.89 L 599.98 233.63 L 603.48 231.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 553.63 280.02 L 520 280.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 558.88 280 L 551.89 283.52 L 553.63 280.02 L 551.87 276.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="560" y="240" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 560 260 L 640 260" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 580 320 L 580 260" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 719.98 233.63 L 719.9 200" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 720 238.88 L 716.48 231.89 L 719.98 233.63 L 723.48 231.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 673.63 280.02 L 640 280.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 678.88 280 L 671.89 283.52 L 673.63 280.02 L 671.87 276.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="680" y="240" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 680 260 L 760 260" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 700 320 L 700 260" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 839.98 233.63 L 839.9 200" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 840 238.88 L 836.48 231.89 L 839.98 233.63 L 843.48 231.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 793.63 280.02 L 760 280.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 798.88 280 L 791.89 283.52 L 793.63 280.02 L 791.87 276.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="800" y="240" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 800 260 L 880 260" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 820 320 L 820 260" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 479.98 353.63 L 479.9 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 480 358.88 L 476.48 351.89 L 479.98 353.63 L 483.48 351.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 433.63 400.02 L 400 400.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 438.88 400 L 431.89 403.52 L 433.63 400.02 L 431.87 396.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="440" y="360" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 440 380 L 520 380" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 460 440 L 460 380" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 599.98 353.63 L 599.9 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 600 358.88 L 596.48 351.89 L 599.98 353.63 L 603.48 351.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 553.63 400.02 L 520 400.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 558.88 400 L 551.89 403.52 L 553.63 400.02 L 551.87 396.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="560" y="360" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 560 380 L 640 380" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 580 440 L 580 380" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 719.98 353.63 L 719.9 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 720 358.88 L 716.48 351.89 L 719.98 353.63 L 723.48 351.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 673.63 400.02 L 640 400.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 678.88 400 L 671.89 403.52 L 673.63 400.02 L 671.87 396.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="680" y="360" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 680 380 L 760 380" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 700 440 L 700 380" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 839.98 353.63 L 839.9 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 840 358.88 L 836.48 351.89 L 839.98 353.63 L 843.48 351.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 793.63 400.02 L 760 400.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 798.88 400 L 791.89 403.52 L 793.63 400.02 L 791.87 396.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="800" y="360" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 800 380 L 880 380" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 820 440 L 820 380" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 479.98 473.63 L 479.9 440" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 480 478.88 L 476.48 471.89 L 479.98 473.63 L 483.48 471.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 433.63 520.02 L 400 520.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 438.88 520 L 431.89 523.52 L 433.63 520.02 L 431.87 516.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="440" y="480" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 440 500 L 520 500" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 460 560 L 460 500" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 599.98 473.63 L 599.9 440" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 600 478.88 L 596.48 471.89 L 599.98 473.63 L 603.48 471.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 553.63 520.02 L 520 520.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 558.88 520 L 551.89 523.52 L 553.63 520.02 L 551.87 516.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="560" y="480" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 560 500 L 640 500" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 580 560 L 580 500" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 719.98 473.63 L 719.9 440" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 720 478.88 L 716.48 471.89 L 719.98 473.63 L 723.48 471.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 673.63 520.02 L 640 520.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 678.88 520 L 671.89 523.52 L 673.63 520.02 L 671.87 516.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="680" y="480" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 680 500 L 760 500" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 700 560 L 700 500" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 839.98 473.63 L 839.9 440" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 840 478.88 L 836.48 471.89 L 839.98 473.63 L 843.48 471.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 793.63 520.02 L 760 520.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 798.88 520 L 791.89 523.52 L 793.63 520.02 L 791.87 516.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="800" y="480" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 800 500 L 880 500" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 820 560 L 820 500" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 479.98 593.63 L 479.9 560" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 480 598.88 L 476.48 591.89 L 479.98 593.63 L 483.48 591.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 433.63 640.02 L 400 640.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 438.88 640 L 431.89 643.52 L 433.63 640.02 L 431.87 636.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="440" y="600" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 440 620 L 520 620" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 460 680 L 460 620" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 599.98 593.63 L 599.9 560" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 600 598.88 L 596.48 591.89 L 599.98 593.63 L 603.48 591.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 553.63 640.02 L 520 640.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 558.88 640 L 551.89 643.52 L 553.63 640.02 L 551.87 636.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="560" y="600" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 560 620 L 640 620" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 580 680 L 580 620" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 719.98 593.63 L 719.9 560" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 720 598.88 L 716.48 591.89 L 719.98 593.63 L 723.48 591.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 673.63 640.02 L 640 640.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 678.88 640 L 671.89 643.52 L 673.63 640.02 L 671.87 636.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="680" y="600" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 680 620 L 760 620" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 700 680 L 700 620" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 839.98 593.63 L 839.9 560" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 840 598.88 L 836.48 591.89 L 839.98 593.63 L 843.48 591.87 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 793.63 640.02 L 760 640.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 798.88 640 L 791.89 643.52 L 793.63 640.02 L 791.87 636.52 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="800" y="600" width="80" height="80" fill="#b1ddf0" stroke="none" pointer-events="all"/><path d="M 800 620 L 880 620" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 820 680 L 820 620" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><rect x="60" y="380" width="261" height="150" fill="#ffffff" stroke="none" pointer-events="none"/><path d="M 60 380 L 321 380 L 321 530 L 60 530 L 60 380" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><path d="M 60 418 L 98 418 L 135 418 L 173 418 L 210 418 L 247 418 L 284 418 L 321 418" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 60 455 L 98 455 L 135 455 L 173 455 L 210 455 L 247 455 L 284 455 L 321 455" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 60 493 L 98 493 L 135 493 L 173 493 L 210 493 L 247 493 L 284 493 L 321 493" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 98 380 L 98 418 L 98 455 L 98 493 L 98 530" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 135 380 L 135 418 L 135 455 L 135 493 L 135 530" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 173 380 L 173 418 L 173 455 L 173 493 L 173 530" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 210 380 L 210 418 L 210 455 L 210 493 L 210 530" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 247 380 L 247 418 L 247 455 L 247 493 L 247 530" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 284 380 L 284 418 L 284 455 L 284 493 L 284 530" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 60 380 M 98 380 M 98 418 M 60 418" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 399px; margin-left: 61px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000" style="font-size: 20px;">03</font></div></div></div></foreignObject><text x="79" y="403" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">03</text></switch></g><path d="M 98 380 M 135 380 M 135 418 M 98 418" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 399px; margin-left: 99px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">02</div></div></div></foreignObject><text x="117" y="405" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">02</text></switch></g><path d="M 135 380 M 173 380 M 173 418 M 135 418" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 399px; margin-left: 136px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">01</div></div></div></foreignObject><text x="154" y="405" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">01</text></switch></g><path d="M 173 380 M 210 380 M 210 418 M 173 418" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 399px; margin-left: 174px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="192" y="405" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 210 380 M 247 380 M 247 418 M 210 418" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 399px; margin-left: 211px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="229" y="405" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 247 380 M 284 380 M 284 418 M 247 418" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 399px; margin-left: 248px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="266" y="405" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 284 380 M 321 380 M 321 418 M 284 418" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 399px; margin-left: 285px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="303" y="405" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 60 418 M 98 418 M 98 455 M 60 455" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 437px; margin-left: 61px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="79" y="443" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 98 418 M 135 418 M 135 455 M 98 455" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 437px; margin-left: 99px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">13</div></div></div></foreignObject><text x="117" y="443" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">13</text></switch></g><path d="M 135 418 M 173 418 M 173 455 M 135 455" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 437px; margin-left: 136px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">12</div></div></div></foreignObject><text x="154" y="443" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">12</text></switch></g><path d="M 173 418 M 210 418 M 210 455 M 173 455" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 437px; margin-left: 174px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">11</div></div></div></foreignObject><text x="192" y="443" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">11</text></switch></g><path d="M 210 418 M 247 418 M 247 455 M 210 455" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 437px; margin-left: 211px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">10</div></div></div></foreignObject><text x="229" y="443" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">10</text></switch></g><path d="M 247 418 M 284 418 M 284 455 M 247 455" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 437px; margin-left: 248px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="266" y="443" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 284 418 M 321 418 M 321 455 M 284 455" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 437px; margin-left: 285px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="303" y="443" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 60 455 M 98 455 M 98 493 M 60 493" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 474px; margin-left: 61px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="79" y="480" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 98 455 M 135 455 M 135 493 M 98 493" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 474px; margin-left: 99px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="117" y="480" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 135 455 M 173 455 M 173 493 M 135 493" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 474px; margin-left: 136px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">23</div></div></div></foreignObject><text x="154" y="480" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">23</text></switch></g><path d="M 173 455 M 210 455 M 210 493 M 173 493" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 474px; margin-left: 174px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">22</div></div></div></foreignObject><text x="192" y="480" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">22</text></switch></g><path d="M 210 455 M 247 455 M 247 493 M 210 493" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 474px; margin-left: 211px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">21</div></div></div></foreignObject><text x="229" y="480" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">21</text></switch></g><path d="M 247 455 M 284 455 M 284 493 M 247 493" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 474px; margin-left: 248px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">20</div></div></div></foreignObject><text x="266" y="480" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">20</text></switch></g><path d="M 284 455 M 321 455 M 321 493 M 284 493" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 474px; margin-left: 285px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="303" y="480" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 60 493 M 98 493 M 98 530 M 60 530" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 512px; margin-left: 61px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="79" y="518" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 98 493 M 135 493 M 135 530 M 98 530" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 512px; margin-left: 99px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="117" y="518" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 135 493 M 173 493 M 173 530 M 135 530" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 512px; margin-left: 136px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="154" y="518" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 173 493 M 210 493 M 210 530 M 173 530" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 512px; margin-left: 174px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">33</div></div></div></foreignObject><text x="192" y="518" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">33</text></switch></g><path d="M 210 493 M 247 493 M 247 530 M 210 530" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 512px; margin-left: 211px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">32</div></div></div></foreignObject><text x="229" y="518" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">32</text></switch></g><path d="M 247 493 M 284 493 M 284 530 M 247 530" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 512px; margin-left: 248px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">31</div></div></div></foreignObject><text x="266" y="518" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">31</text></switch></g><path d="M 284 493 M 321 493 M 321 530 M 284 530" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 512px; margin-left: 285px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">30</div></div></div></foreignObject><text x="303" y="518" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">30</text></switch></g><rect x="920" y="30" width="261" height="150" fill="#ffffff" stroke="none" pointer-events="none"/><path d="M 920 30 L 1181 30 L 1181 180 L 920 180 L 920 30" fill="none" stroke="#000000" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><path d="M 920 68 L 958 68 L 995 68 L 1033 68 L 1070 68 L 1107 68 L 1144 68 L 1181 68" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 920 105 L 958 105 L 995 105 L 1033 105 L 1070 105 L 1107 105 L 1144 105 L 1181 105" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 920 143 L 958 143 L 995 143 L 1033 143 L 1070 143 L 1107 143 L 1144 143 L 1181 143" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 958 30 L 958 68 L 958 105 L 958 143 L 958 180" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 995 30 L 995 68 L 995 105 L 995 143 L 995 180" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1033 30 L 1033 68 L 1033 105 L 1033 143 L 1033 180" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1070 30 L 1070 68 L 1070 105 L 1070 143 L 1070 180" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1107 30 L 1107 68 L 1107 105 L 1107 143 L 1107 180" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1144 30 L 1144 68 L 1144 105 L 1144 143 L 1144 180" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 920 30 M 958 30 M 958 68 M 920 68" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 49px; margin-left: 921px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000" style="font-size: 20px;">30</font></div></div></div></foreignObject><text x="939" y="53" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">30</text></switch></g><path d="M 958 30 M 995 30 M 995 68 M 958 68" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 49px; margin-left: 959px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">20</div></div></div></foreignObject><text x="977" y="55" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">20</text></switch></g><path d="M 995 30 M 1033 30 M 1033 68 M 995 68" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 49px; margin-left: 996px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">10</div></div></div></foreignObject><text x="1014" y="55" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">10</text></switch></g><path d="M 1033 30 M 1070 30 M 1070 68 M 1033 68" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 49px; margin-left: 1034px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1052" y="55" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1070 30 M 1107 30 M 1107 68 M 1070 68" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 49px; margin-left: 1071px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1089" y="55" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1107 30 M 1144 30 M 1144 68 M 1107 68" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 49px; margin-left: 1108px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1126" y="55" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1144 30 M 1181 30 M 1181 68 M 1144 68" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 49px; margin-left: 1145px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1163" y="55" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 920 68 M 958 68 M 958 105 M 920 105" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 87px; margin-left: 921px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="939" y="93" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 958 68 M 995 68 M 995 105 M 958 105" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 87px; margin-left: 959px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">31</div></div></div></foreignObject><text x="977" y="93" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">31</text></switch></g><path d="M 995 68 M 1033 68 M 1033 105 M 995 105" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 87px; margin-left: 996px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">21</div></div></div></foreignObject><text x="1014" y="93" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">21</text></switch></g><path d="M 1033 68 M 1070 68 M 1070 105 M 1033 105" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 87px; margin-left: 1034px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">11</div></div></div></foreignObject><text x="1052" y="93" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">11</text></switch></g><path d="M 1070 68 M 1107 68 M 1107 105 M 1070 105" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 87px; margin-left: 1071px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">01</div></div></div></foreignObject><text x="1089" y="93" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">01</text></switch></g><path d="M 1107 68 M 1144 68 M 1144 105 M 1107 105" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 87px; margin-left: 1108px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1126" y="93" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1144 68 M 1181 68 M 1181 105 M 1144 105" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 87px; margin-left: 1145px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1163" y="93" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 920 105 M 958 105 M 958 143 M 920 143" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 124px; margin-left: 921px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="939" y="130" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 958 105 M 995 105 M 995 143 M 958 143" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 124px; margin-left: 959px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="977" y="130" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 995 105 M 1033 105 M 1033 143 M 995 143" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 124px; margin-left: 996px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">32</div></div></div></foreignObject><text x="1014" y="130" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">32</text></switch></g><path d="M 1033 105 M 1070 105 M 1070 143 M 1033 143" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 124px; margin-left: 1034px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">22</div></div></div></foreignObject><text x="1052" y="130" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">22</text></switch></g><path d="M 1070 105 M 1107 105 M 1107 143 M 1070 143" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 124px; margin-left: 1071px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">12</div></div></div></foreignObject><text x="1089" y="130" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">12</text></switch></g><path d="M 1107 105 M 1144 105 M 1144 143 M 1107 143" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 124px; margin-left: 1108px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">02</div></div></div></foreignObject><text x="1126" y="130" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">02</text></switch></g><path d="M 1144 105 M 1181 105 M 1181 143 M 1144 143" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 124px; margin-left: 1145px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 34px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1163" y="130" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 920 143 M 958 143 M 958 180 M 920 180" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 162px; margin-left: 921px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="939" y="168" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 958 143 M 995 143 M 995 180 M 958 180" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 162px; margin-left: 959px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="977" y="168" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 995 143 M 1033 143 M 1033 180 M 995 180" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 36px; height: 1px; padding-top: 162px; margin-left: 996px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">00</div></div></div></foreignObject><text x="1014" y="168" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">00</text></switch></g><path d="M 1033 143 M 1070 143 M 1070 180 M 1033 180" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 162px; margin-left: 1034px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">33</div></div></div></foreignObject><text x="1052" y="168" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">33</text></switch></g><path d="M 1070 143 M 1107 143 M 1107 180 M 1070 180" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 162px; margin-left: 1071px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">23</div></div></div></foreignObject><text x="1089" y="168" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">23</text></switch></g><path d="M 1107 143 M 1144 143 M 1144 180 M 1107 180" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 162px; margin-left: 1108px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">13</div></div></div></foreignObject><text x="1126" y="168" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">13</text></switch></g><path d="M 1144 143 M 1181 143 M 1181 180 M 1144 180" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 35px; height: 1px; padding-top: 162px; margin-left: 1145px;"><div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center; max-height: 33px; overflow: hidden;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">03</div></div></div></foreignObject><text x="1163" y="168" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle">03</text></switch></g><path d="M 59.74 399.87 L 20 400 L 20 280 L 388.03 280" fill="none" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 395.53 280 L 385.53 285 L 388.03 280 L 385.53 275 Z" fill="#000000" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 59.22 439.94 L 40 440 L 40 320 L 360 320 L 360 400 L 388.03 400" fill="none" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 395.53 400 L 385.53 405 L 388.03 400 L 385.53 395 Z" fill="#000000" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 59.48 510.02 L 20 510 L 20 640 L 388.03 640" fill="none" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 395.53 640 L 385.53 645 L 388.03 640 L 385.53 635 Z" fill="#000000" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 60.26 470.05 L 40 470 L 40 600 L 360 600 L 360 520 L 388.03 520" fill="none" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 395.53 520 L 385.53 525 L 388.03 520 L 385.53 515 Z" fill="#000000" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 920 49.99 L 480 50 L 480 188.03" fill="none" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 480 195.53 L 475 185.53 L 480 188.03 L 485 185.53 Z" fill="#000000" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 920.26 90.2 L 600 90 L 600 188.03" fill="none" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 600 195.53 L 595 185.53 L 600 188.03 L 605 185.53 Z" fill="#000000" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 919.22 120.85 L 720 120 L 720 188.03" fill="none" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 720 195.53 L 715 185.53 L 720 188.03 L 725 185.53 Z" fill="#000000" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 920 161.5 L 840 162 L 840 188.03" fill="none" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><path d="M 840 195.53 L 835 185.53 L 840 188.03 L 845 185.53 Z" fill="#000000" stroke="#000000" stroke-width="4" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 280px; margin-left: 451px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="480" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 280px; margin-left: 571px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="600" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 280px; margin-left: 691px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="720" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 280px; margin-left: 811px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="840" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 400px; margin-left: 451px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="480" y="404" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 400px; margin-left: 571px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="600" y="404" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 400px; margin-left: 691px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="720" y="404" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 395px; margin-left: 811px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="840" y="399" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 520px; margin-left: 451px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="480" y="524" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 520px; margin-left: 571px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="600" y="524" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 520px; margin-left: 691px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="720" y="524" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 520px; margin-left: 811px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="840" y="524" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 640px; margin-left: 451px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="480" y="644" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 640px; margin-left: 571px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="600" y="644" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 640px; margin-left: 691px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="720" y="644" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 640px; margin-left: 811px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font color="#000000">PE</font></div></div></div></foreignObject><text x="840" y="644" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">PE</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>