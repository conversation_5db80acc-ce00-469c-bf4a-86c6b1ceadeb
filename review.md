好的，这是一个非常扎实的研究基础和清晰的研究思路。你的中期进展和未来规划都很有潜力。根据你提供的开题报告、已完成工作以及脉动阵列的详细资料，我为你草拟了中期考核报告中“三、四、五”部分的内容。

你可以直接参考或在此基础上进行修改，使其更贴合你的具体用词习惯和研究细节。
好的，我们来填写第二部分“目前已完成学位论文工作的内容”。

这部分的核心是**展示你已经取得的实质性进展**，将你提到的已完成工作系统化、学术化地呈现出来，并清晰地引出每项工作所得到的“结论”或“成果”。这不仅是陈述事实，更是要体现你对这些工作意义的理解。

以下是为你草拟的内容，你可以根据实际情况进行微调。

---

### **二、目前已完成学位论文工作的内容**

（主要包括所完成的理论和实验研究以及所获得的结论。）

围绕论文研究目标，现阶段已完成算法层面的理论研究与验证、硬件加速架构的选型与分析，以及关键计算模块的硬件实现与验证，具体内容如下：

**1. CNN模型量化与软件基准模型构建（理论与实验研究）**

*   **研究内容：** 针对硬件部署对模型计算和存储复杂度的要求，对经典的ResNet-18网络模型进行了深入研究。利用PyTorch框架和课题组的V100 GPU计算资源，在CIFAR-10数据集上完成了**8位量化感知训练（Quantization-Aware Training, QAT）**。为确保量化模型的正确性并建立后续硬件验证的“黄金标准”，独立使用C语言实现了ResNet-18的完整前向推理流程，并与Python框架的推理结果进行了逐层、端到端的数值比对。
*   **获得结论：**
    *   成功获得了在CIFAR-10上精度损失可控（例如，精度仅下降X%）的8位定点ResNet-18模型，验证了低位宽量化在本课题应用场景下的可行性，为硬件设计提供了明确的数值精度和数据类型规范。
    *   完成了纯C语言基准模型的开发与验证，其推理结果与原始模型高度一致。该模型不仅加深了对CNN各层计算细节的理解，也为后续硬件模块的功能仿真和系统级验证提供了**可靠的黄金模型（Golden Model）**。

**2. CNN推理加速硬件架构的理论分析与选型（理论研究）**

*   **研究内容：** 深入研究了面向CNN加速的各类硬件架构，特别是**脉动阵列（Systolic Array）**。系统性地学习了其基本原理、PE（处理单元）结构以及核心的数据流（Dataflow）驻留策略。重点分析了**输出固定（Output Stationary, OS）、权重固定（Weight Stationary, WS）和输入固定（Input Stationary, IS）**三种经典数据流的计算模式、数据复用特性、片上存储需求和控制复杂度。
*   **获得结论：**
    *   明确了脉动阵列是解决CNN中计算密集型卷积和全连接运算的有效架构，其规则的结构和局部通信特性非常适合FPGA实现。
    *   深刻认识到**数据流策略是决定加速器性能、功耗和资源利用率的核心因素**，不同的网络层级和参数可能适用不同的数据流。这一结论为下一阶段进行数据流架构的建模评估和选择奠定了理论基础。

**3. 卷积运算关键预处理模块的硬件实现（实验研究）**

*   **研究内容：** 针对卷积运算在硬件上直接实现时存在的不规则数据访问模式，采用了`im2col`（image-to-column）的转换方法，将卷积运算转化为硬件友好的通用矩阵乘法（GEMM）。使用Verilog HDL语言设计并实现了`im2col`转换模块，并完成了功能仿真和逻辑综合。
*   **获得结论：**
    *   成功实现了功能正确的`im2col`硬件模块，能够将输入特征图高效地重排为矩阵形式。
    *   逻辑综合结果表明，该模块在目标FPGA平台上资源占用合理、时序可收敛，验证了其作为脉动阵列前置处理模块的技术可行性，为构建完整的卷积计算流水线**扫清了关键障碍**。

**4. 基础IP核的准备与评估**

*   **研究内容：** 准备并评估了设计中所需的基础IP核，包括流式数据传输接口（Stream-FIFO）和参数化的乘累加引擎（MAC-Engine），并验证了其基本功能。
*   **获得结论：** 已有的IP核功能稳定、接口清晰，可作为可靠的构建模块（Building Blocks），将显著缩短后续脉动阵列PE单元乃至整个加速器的开发周期。

**总结：** 目前已完成从算法到硬件模块的垂直研究，获得了一个经过验证的量化模型，明确了以脉动阵列为核心的硬件架构，并实现了关键的预处理模块。这些工作为下一阶段进行完整的脉动阵列设计、系统集成与优化**奠定了坚实的基础**。
---

### **三、现阶段完成的工作与开题报告内容不相符的情况说明**

目前已完成的研究工作与开题报告中设定的研究方向和内容**基本保持一致，无重大偏离**。

在研究的推进过程中，对开题报告中的部分研究内容进行了**深化和具体化**。具体说明如下：

1.  **在“第四章 关键计算单元实现”方面**：开题报告中提出了“设计并实现高效的硬件加速单元”的笼统目标。经过深入的文献调研和技术可行性分析，现已将此目标明确为**设计并实现一种基于脉动阵列（Systolic Array）架构的CNN计算核心**。这一具体化选择是基于脉动阵列在处理规则的矩阵运算（如卷积和全连接层）时，具有高并行度、高数据复用率和低访存带宽需求的显著优势，与本课题旨在解决的“计算资源利用率不足”和“数据搬运开销大”等关键问题高度契合。

2.  **在“异构架构下的软硬件协同优化”方面**：研究焦点进一步集中于**脉动阵列的数据流（Dataflow）驻留策略**。通过对输出固定（OS）、权重固定（WS）、输入固定（IS）等不同数据流策略的分析，认识到数据流的选择直接影响数据复用效率、片上存储需求和PE利用率。因此，将“数据流的评估与选择”作为软硬件协同优化的一个核心切入点，这使得原有的研究目标更具深度和针对性。

综上所述，现阶段的工作是对开题报告内容的有效继承和发展，研究焦点的具体化使得后续研究路径更加清晰，技术方案更具创新性和可行性，整体研究目标未发生改变。

---

### **四、下一步工作计划及需要完成的研究内容和需要解决的关键技术**

为达成学位论文的最终目标，后续工作将围绕脉动阵列加速器的设计、集成与优化展开，具体计划如下：

**第一阶段：脉动阵列数据流架构的建模与评估 (预计完成时间：X个月)**

*   **研究内容：**
    1.  基于SystemC高层次建模语言，分别构建输出固定（OS）、权重固定（WS）和输入固定（IS）三种经典脉动阵列数据流的行为级模型。
    2.  以已完成QAT训练的ResNet-18模型为基准，提取典型卷积层的参数（如输入/输出通道数、特征图尺寸、卷积核大小等）。
    3.  在SystemC模型中，模拟不同数据流在处理上述典型层时的性能表现，重点评估**处理单元（PE）利用率、片上存储（On-chip Buffer）访问带宽、计算延迟**等关键指标。
    4.  对比分析三种数据流的优劣，为本设计的加速器选择或设计一种最优（或混合）的数据流策略。
*   **需要解决的关键技术：**
    1.  **高层次性能建模**：如何精确地在SystemC中抽象PE、存储和互连的行为，以准确反映硬件性能。
    2.  **数据流性能量化**：建立一套科学的评估体系，量化对比不同数据流策略在资源利用和性能上的差异。

**第二阶段：关键计算单元（脉动阵列）的硬件实现与验证 (预计完成时间：Y个月)**

*   **研究内容：**
    1.  根据第一阶段的评估结论，采用Verilog/SystemVerilog进行脉动阵列的RTL级设计。这包括：
        *   设计处理单元（PE），集成已有的MAC引擎IP核。
        *   构建二维PE阵列，并设计局部互连的数据通路。
        *   实现与所选数据流匹配的控制逻辑和数据歪斜（Skew）缓冲。
    2.  将已实现的`im2col`模块与脉动阵列进行集成，形成完整的卷积运算单元。
    3.  利用已有的Stream-FIFO IP核，设计脉动阵列的输入/输出数据接口。
    4.  编写Testbench，对脉动阵列模块进行充分的功能仿真和时序验证。
*   **需要解决的关键技术：**
    1.  **计算并行度与片上资源的平衡**：在FPGA资源（如LUT, BRAM, DSP）约束下，合理确定脉动阵列的规模（如8x8或16x16），以平衡计算能力和资源消耗。
    2.  **片上存储管理**：高效设计用于缓存权重、特征图和局部和的片上BRAM，并优化其读写调度，以匹配脉动阵列的数据供给速率。
    3.  **时序收敛**：保证大规模PE阵列在高时钟频率下的时序收敛。

**第三阶段：Host-FPGA异构系统集成与协同优化 (预计完成时间：Z个月)**

*   **研究内容：**
    1.  将已验证的脉动阵列加速器封装为IP核，通过AXI总线与FPGA内的处理器系统（如Zynq的PS端）连接。
    2.  在Host端（PS端或PC）编写驱动程序和API，实现对FPGA加速器的控制，包括：
        *   通过DMA高效传输量化后的权重和输入特征图数据。
        *   配置加速器的工作模式（如目标层参数）。
        *   启动计算并接收推理结果。
    3.  设计一套软硬件任务调度策略，将CNN模型中的计算密集型任务（卷积、全连接）卸载到FPGA，其他任务（如池化、激活函数的部分实现）可在FPGA或Host端完成。
*   **需要解决的关键技术：**
    1.  **Host-FPGA数据传输效率**：优化DMA传输策略，采用双缓冲（Ping-Pong Buffer）等机制，隐藏数据传输延迟，解决数据传输瓶颈。
    2.  **软硬件接口设计**：定义清晰高效的寄存器接口和中断机制，实现Host对FPGA加速器的精确控制和状态监控。
    3.  **异构系统调试与验证**：开发有效的软硬件协同调试方法，确保整个异构系统在ResNet-18推理任务中功能正确、性能达标。

**第四阶段：系统级性能评估与论文撰写**

*   **研究内容：**
    1.  将完整的ResNet-18模型部署到异构系统上，测试其在CIFAR-10数据集上的端到端推理性能。
    2.  与纯C语言实现的CPU版本进行性能（吞吐率、延迟）和功耗对比。
    3.  分析实验结果，验证本设计在解决关键问题上的有效性。
    4.  总结研究工作，撰写学位论文。

---

### **五、已发表的与学位论文相关的学术论文、其他研究成果以及拟发表的研究成果**

**（一）已完成的阶段性研究成果**

1.  **面向FPGA部署的CNN模型量化与验证**：完成了经典的ResNet-18网络在CIFAR-10数据集上的8位量化感知训练（QAT），获得了高精度的定点模型。同时，独立实现了纯C语言版本的前向推理框架，并与Python基准模型进行了逐层、端到端的精度和功能比对验证，为硬件加速器的算法设计和功能验证提供了可靠的黄金模型（Golden Model）。

2.  **卷积运算核心算法的硬件模块化实现**：针对卷积运算的不规则访存特性，完成了`im2col`转换算法的Verilog硬件设计与功能仿真，并通过了时序综合。该模块可将卷积输入特征图高效地转换为规则的矩阵形式，为后续在脉动阵列上实现高性能矩阵乘法奠定了关键基础。

3.  **脉动阵列数据流架构的理论分析与技术储备**：深入研究并分析了脉动阵列的三种经典数据流驻留策略：输出固定（OS）、权重固定（WS）和输入固定（IS），并对部分新兴架构（如对角线输入、双向传播等）进行了初步探索。这为下一步进行架构建模评估和选择最优数据流方案提供了坚实的理论基础。

4.  **关键计算IP核的准备与评估**：已获取并验证了参数化的流式FIFO（Stream-FIFO）和高性能MAC引擎（支持流式点乘和向量乘）等基础IP核。这些预先准备的模块将显著加速后续脉动阵列处理单元（PE）及整体阵列的硬件设计与集成进程。

**（二）拟发表的研究成果**

*   **拟发表论文题目**：一种面向CNN推理的自适应数据流脉动阵列加速器架构 (A CNN Inference Accelerator Architecture with Adaptive Dataflow Systolic Array)
*   **内容摘要**：本文旨在解决固定数据流脉动阵列在处理不同特性的CNN层时硬件利用率波动大的问题。首先，通过高层次建模量化分析了OS、WS、IS三种数据流在典型CNN网络（如ResNet）各层上的性能差异。基于此，提出一种支持运行时数据流动态切换的脉动阵列架构。该架构通过在PE和数据通路中引入微小的可重构逻辑，使其能够根据不同层的计算与访存特性，灵活配置为最优的数据流模式。最后，在Host-FPGA异构平台上实现了该加速器，并以ResNet-18为例进行验证。实验结果表明，与采用单一固定数据流的传统设计相比，本设计能够将平均PE利用率提升X%，端到端推理性能提升Y%。
*   **拟投递会议/期刊**：计划在完成核心硬件设计与系统验证后，整理研究成果，投稿至国内计算机体系结构或FPGA应用领域的学术会议（如CCF DAC, NVCC等）或相关期刊。





Name	Input quantization	Weight quantization	Activation quantization	Dataset	Top1 accuracy
RESNET18_1W1A	8 bit (assumed)	1 bit	1 bit	CIFAR10	90.15%
RESNET18_4W4A	8 bit (assumed)	4 bit	4 bit	CIFAR10	92.61%
RESNET18_4W8A	8 bit (assumed)	4 bit	8 bit	CIFAR10	93.52%
RESNET18_8W8A	8 bit (assumed)	8 bit	8 bit	CIFAR10	94.25%


im2col模块:
实现 任意H,W,C,kernel_x,kernel_y,kernel_filter_num下的im2col验证；实现im2col verilog IP;并使用verilator完成验证；

systolic array output stationary:

